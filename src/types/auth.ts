export interface User {
  id: string;
  email: string;
  first_name?: string;
  name?: string;
}

export interface LoginResponse {
  access: string;        // from backend on login
  refresh?: string;      // optional
  user?: User;           // some backends include user on login, others don't
}

export interface AuthState {
  user: User | null;
  token: string | null;      // primary token used by prepareHeaders
  access?: string | null;    // kept for compatibility with existing code
  refresh?: string | null;   // kept for compatibility with existing code
  isAuthenticated: boolean;
  isLoading: boolean;
}
