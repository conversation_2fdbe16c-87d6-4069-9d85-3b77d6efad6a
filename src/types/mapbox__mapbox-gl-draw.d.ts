declare module '@mapbox/mapbox-gl-draw' {
  import type { IControl, Map } from 'mapbox-gl';
  import type { Feature, FeatureCollection } from 'geojson';

  export interface DrawControls {
    point?: boolean;
    line_string?: boolean;
    polygon?: boolean;
    trash?: boolean;
    combine_features?: boolean;
    uncombine_features?: boolean;
  }

  export interface DrawOptions {
    displayControlsDefault?: boolean;
    keybindings?: boolean;
    touchEnabled?: boolean;
    boxSelect?: boolean;
    clickBuffer?: number;
    touchBuffer?: number;
    controls?: DrawControls;
    styles?: object[];
    modes?: object;
    defaultMode?: string;
    userProperties?: boolean;
  }

  export default class MapboxDraw implements IControl {
    constructor(options?: DrawOptions);
    onAdd(map: Map): HTMLElement;
    onRemove(map: Map): void;
    getDefaultPosition(): ControlPosition;
    add(geojson: object): string[];
    get(id: string): Feature | undefined;
    getAll(): FeatureCollection;
    getSelected(): FeatureCollection;
    getSelectedPoints(): FeatureCollection;
    getSelectedIds(): string[];
    getFeatureIdsAt(point: { x: number; y: number }): string[];
    set(fc: FeatureCollection): string[];
    delete(ids: string | string[]): this;
    deleteAll(): this;
    changeMode(mode: string, options?: object): this;
    setFeatureProperty(id: string, prop: string, value: any): this;
    combineFeatures(): this;
    uncombineFeatures(): this;
    trash(): this;
    getMode(): string;
  }
}
