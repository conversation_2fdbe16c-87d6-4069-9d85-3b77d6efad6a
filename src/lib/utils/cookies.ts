export type AuthTokens = {
  token?: string;
  access?: string | null;
  refresh?: string | null;
  auth?: string | null;
};

export function getValidAuthTokens(): AuthTokens {
  if (typeof document === 'undefined') return {};
  const map = Object.fromEntries(
    document.cookie.split('; ').filter(Boolean).map((c) => {
      const i = c.indexOf('=');
      return [c.slice(0, i), decodeURIComponent(c.slice(i + 1))];
    })
  ) as Record<string, string>;

  const access = map['access-token'] ?? map['auth_token'] ?? null;
  const refresh = map['refresh-token'] ?? null;
  const auth = map['auth_token'] ?? null;

  return {
    token: access ?? auth ?? undefined,
    access,
    refresh,
    auth,
  };
}
