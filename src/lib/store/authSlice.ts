// src/lib/store/authSlice.ts
import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import { setCookie, deleteCookie } from 'cookies-next';
import { AuthState, LoginResponse } from '@/types/auth';
import { authApi } from './authApi';

const initialState: AuthState = {
  user: null,
  token: null,
  access: null,
  refresh: null,
  isAuthenticated: false,
  isLoading: false,
};

const setAuthCookie = (token: string) => {
  // Optional: Base64 for demonstration only; consider HttpOnly cookies on the server for production
  const toBase64 = Buffer.from(token).toString('base64');
  setCookie('auth_token', toBase64, {
    maxAge: 7 * 24 * 60 * 60,
    path: '/',
    sameSite: 'strict',
    secure: process.env.NODE_ENV === 'production',
  });
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    logout: () => {
      deleteCookie('auth_token');
      return initialState;
    },
    setCredentials: (state, action: PayloadAction<LoginResponse>) => {
      const { access, refresh, user } = action.payload;
      state.user = user ?? state.user;
      state.token = access ?? null;      // keep token in sync for prepareHeaders
      state.access = access ?? null;     // keep for compatibility with existing code
      state.refresh = refresh ?? null;   // keep for compatibility with existing code
      state.isAuthenticated = !!access;
      state.isLoading = false;
      if (access) setAuthCookie(access);
    },
  },
  extraReducers: (builder) => {
    builder
      .addMatcher(authApi.endpoints.login.matchPending, (state) => {
        state.isLoading = true;
      })
      .addMatcher(authApi.endpoints.login.matchFulfilled, (state, { payload }) => {
        const { access, refresh, user } = payload;
        state.token = access ?? null;      // critical: prepareHeaders reads state.auth.token
        state.access = access ?? null;
        state.refresh = refresh ?? null;
        state.user = user ?? state.user;   // some backends return user here
        state.isAuthenticated = !!access;
        state.isLoading = false;
        if (access) setAuthCookie(access);
      })
      .addMatcher(authApi.endpoints.login.matchRejected, (state:any) => {
        state.isLoading = false;
      })
      .addMatcher(
        authApi.endpoints.getAuthData.matchFulfilled,
        (state:any, { payload }:any) => {
          // Accept either { user } or a user object
          const userFromPayload = (payload as any)?.user ?? payload;
          state.user = userFromPayload ?? state.user;
          state.isAuthenticated = !!state.token;

          // Optional debug persistence
          if (typeof window !== 'undefined') {
            try {
              localStorage.setItem('data', JSON.stringify(state.user));
              if (state.access) localStorage.setItem('access', state.access);
              if (state.refresh) localStorage.setItem('refresh', state.refresh);
            } catch { }
          }
        }
      );
  },
});

export const { logout, setCredentials } = authSlice.actions;
export default authSlice.reducer;
