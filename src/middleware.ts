import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Paths not require authentication
const PUBLIC_ROUTES = ['/', '/auth', '/activate-message', '/Activate'];

export function middleware(request: NextRequest) {
  
  const { pathname } = request.nextUrl;

  
  if (PUBLIC_ROUTES.includes(pathname)) {
    return NextResponse.next();
  }

  const accessToken = request.cookies.get('access-token');
  const authToken = request.cookies.get('auth_token');  
  const refreshToken = request.cookies.get('refresh-token');
  
  const hasAuth = accessToken || authToken || refreshToken;

  
  if (!hasAuth) {
    return NextResponse.redirect(new URL('/auth', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};
