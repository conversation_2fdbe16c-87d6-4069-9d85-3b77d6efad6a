"use client";

import { useSearchParams } from "next/navigation";
import { useState } from "react";
import {useRouter} from "next/navigation"
// export default function ConfirmPage() {
//   const searchParams = useSearchParams();
//   const uid = searchParams.get("uid");
//   const token = searchParams.get("token");

//   const [loading, setLoading] = useState(false);
//   const [message, setMessage] = useState("");

//   const handleConfirm = async () => {
//     if (!uid || !token) {
//       setMessage("Invalid confirmation link");
//       return;
//     }

//     setLoading(true);
//     try {
//       const res = await fetch("https://example.com/api/confirm-email/", {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//         },
//         body: JSON.stringify({ uid, token }),
//       });

//       if (!res.ok) throw new Error("Failed to confirm");

//       const data = await res.json();
//       setMessage("✅ " + data.message);
//     } catch (error) {
//       setMessage("❌ Something went wrong");
//     } finally {
//       setLoading(false);
//     }
//   };

//   return (
//     <div className="flex flex-col items-center justify-center min-h-screen">
//       <h1 className="text-xl font-bold mb-4">Email Confirmation</h1>
//       <button
//         onClick={handleConfirm}
//         disabled={loading}
//         className="bg-green-600 text-white px-6 py-2 rounded-lg shadow-md disabled:opacity-50"
//       >
//         {loading ? "Confirming..." : "Confirm Email"}
//       </button>
//       {message && <p className="mt-4">{message}</p>}
//     </div>
//   );
// }

// import React, { useState } from 'react';

const FarmtiConfirmButton = ({
  confirmationLink = "https://farmti.com/confirm-email",
  isLoading = false,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);

  const router = useRouter();
  const searchParams = useSearchParams();
  const id = searchParams.get("id");
  const token = searchParams.get("token");

  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");

  const handleConfirm = async () => {
    if (!id || !token) {
      setMessage("Invalid confirmation link");
      return;
    }

    // alert("TTTTTTTT");

    setLoading(true);
    try {
      // TODO: Use actual backend URL
      const res = await fetch(`http://127.0.0.1:8080/v1/users/activate/${id}/${token}`);

      if (!res.ok) throw new Error("Failed to confirm");
      // alert("success");
      if (res.status ===204)router.push("/auth");

      const data = await res.json();
      setMessage("✅ " + data.message);
    } catch (error) {
      setMessage("❌ Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-gradient-to-br from-green-50 via-emerald-50 to-green-100 p-4">
      <div className="flex flex-col items-center p-8 bg-white/80 backdrop-blur-sm rounded-xl shadow-2xl max-w-md w-full border border-green-200">
        {/* Farmti Logo/Brand */}
        <div className="text-center mb-6">
          <h1 className="text-3xl font-bold text-green-800 tracking-wider mb-2">
            🌾 FARMTI
          </h1>
          <p className="text-green-600 text-sm font-medium">
            Smart Farm Management Platform
          </p>
        </div>

        {/* Confirmation Message */}
        <div className="text-center mb-6">
          <h2 className="text-lg font-semibold text-green-900 mb-2">
            Confirm Your Email Address
          </h2>
          <p className="text-green-700 text-sm">
            Activate your account to start managing your farm with IoT sensors
          </p>
        </div>

        {/* Main Confirmation Button */}
        <button
          onClick={ async () =>  {await handleConfirm()}}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          onMouseDown={() => setIsPressed(true)}
          onMouseUp={() => setIsPressed(false)}
          disabled={isLoading}
          className={`
            relative overflow-hidden
            w-full px-8 py-4
            bg-gradient-to-r from-green-600 to-green-700
            hover:from-green-700 hover:to-green-800
            active:from-green-800 active:to-green-900
            text-white font-bold text-lg
            rounded-xl shadow-lg
            transition-all duration-300 ease-in-out
            transform hover:scale-105 active:scale-95
            border-2 border-green-500
            ${isLoading ? "opacity-70 cursor-not-allowed" : "cursor-pointer"}
            ${isHovered ? "shadow-2xl shadow-green-400/30" : ""}
          `}
        >
          {/* Button Background Effect */}
          <div
            className={`
            absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent
            transform -skew-x-12 transition-transform duration-700
            ${isHovered ? "translate-x-full" : "-translate-x-full"}
            opacity-20
          `}
          />

          {/* Button Content */}
          <div className="relative flex items-center justify-center space-x-3">
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent" />
                <span>Processing...</span>
              </>
            ) : (
              <>
                <span className="text-2xl">✓</span>
                <span>Confirm Email & Start Farming</span>
                <span className="text-xl">🚜</span>
              </>
            )}
          </div>
        </button>

        {/* Security Notice */}
        <div className="mt-6 text-center">
          <p className="text-green-600 text-xs">
            🔒 Secure confirmation • Link expires in 24 hours
          </p>
        </div>

        {/* Alternative Text Link */}
        <div className="mt-4 text-center">
          <p className="text-green-700 text-xs mb-2">
            Having trouble with the button?
          </p>
          <button
            onClick={() => {handleConfirm()}}
            className="text-green-600 hover:text-green-800 underline text-sm font-medium transition-colors duration-200"
          >
            Click here to confirm your Farmti account →
          </button>
        </div>

        {/* IoT Sensors Visual Elements */}
        <div className="mt-6 flex justify-center space-x-6 opacity-60">
          <div className="text-green-500 text-center">
            <div className="text-2xl mb-1">💧</div>
            <div className="text-xs">Water</div>
          </div>
          <div className="text-green-500 text-center">
            <div className="text-2xl mb-1">🌡️</div>
            <div className="text-xs">Temperature</div>
          </div>
          <div className="text-green-500 text-center">
            <div className="text-2xl mb-1">📊</div>
            <div className="text-xs">Analytics</div>
          </div>
          <div className="text-green-500 text-center">
            <div className="text-2xl mb-1">🛰️</div>
            <div className="text-xs">IoT Sensors</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FarmtiConfirmButton;
