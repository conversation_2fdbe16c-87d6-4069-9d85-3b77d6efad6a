@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

:root {
  --background: 120 20% 97%;
  --foreground: 220 13% 13%;
  --card: 0 0% 100%;
  --card-foreground: 220 13% 13%;

  --popover: 0 0% 100%;
  --popover-foreground: 220 13% 13%;

  /* Primary Green - Main brand color */
  --primary: 142 71% 45%;
  --primary-foreground: 0 0% 100%;
  --primary-light: 142 71% 55%;
  --primary-dark: 142 71% 35%;

  /* Earth tones */
  --secondary: 32 22% 94%;
  --secondary-foreground: 220 13% 13%;

  --muted: 32 22% 94%;
  --muted-foreground: 215 16% 47%;

  --accent: 195 53% 79%;
  --accent-foreground: 220 13% 13%;

  /* Status colors */
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 100%;

  --warning: 43 96% 56%;
  --warning-foreground: 220 13% 13%;

  --success: 142 71% 45%;
  --success-foreground: 0 0% 100%;

  /* Form elements */
  --border: 32 22% 88%;
  --input: 0 0% 100%;
  --input-border: 32 22% 88%;
  --ring: 142 71% 45%;

  --radius: 0.75rem;

  /* Farm theme gradients */
  --gradient-farm: linear-gradient(135deg, hsl(120 40% 92%), hsl(195 53% 87%));
  --gradient-primary: linear-gradient(
    135deg,
    hsl(142 71% 45%),
    hsl(142 71% 55%)
  );

  /* Shadows */
  --shadow-soft: 0 4px 6px -1px hsl(142 71% 45% / 0.1),
    0 2px 4px -1px hsl(142 71% 45% / 0.06);
  --shadow-medium: 0 10px 15px -3px hsl(142 71% 45% / 0.1),
    0 4px 6px -2px hsl(142 71% 45% / 0.05);
  --shadow-large: 0 20px 25px -5px hsl(142 71% 45% / 0.1),
    0 10px 10px -5px hsl(142 71% 45% / 0.04);

  /* Animation variables */
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --transition-fast: all 0.15s ease-out;

  --sidebar-background: 0 0% 98%;

  --sidebar-foreground: 240 5.3% 26.1%;

  --sidebar-primary: 240 5.9% 10%;

  --sidebar-primary-foreground: 0 0% 98%;

  --sidebar-accent: 240 4.8% 95.9%;

  --sidebar-accent-foreground: 240 5.9% 10%;

  --sidebar-border: 220 13% 91%;

  --sidebar-ring: 217.2 91.2% 59.8%;
}

* {
  /* margin: 0; */
  /* padding: 0; */
  box-sizing: border-box;
  font-family: "Poppins", sans-serif;
  text-decoration: none;
  list-style: none;
}

body {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(90deg, #e2e2e2, #c9d6ff);
}

.sliding-auth-container {
  position: relative;
  width: 850px;
  height: 550px;
  max-width: 90vw;
  background: hsl(var(--background));
  border-radius: 30px;
  box-shadow: 0 0 30px hsl(var(--foreground) / 0.1);
  overflow: hidden;
  margin: 20px;
}

.container {
  position: relative;
  width: 850px;
  height: 550px;
  background: #fff;
  margin: 20px;
  border-radius: 30px;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.container h1 {
  font-size: 36px;
  margin: -10px 0;
}
.farm-background {
  background: var(--gradient-farm);
  position: relative;
  overflow: hidden;
}
.farm-background::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%2322c55e' fill-opacity='0.03' fill-rule='evenodd'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E"),
    url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2387ceeb' fill-opacity='0.02'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  background-size: 80px 80px, 120px 120px;
  background-position: 0 0, 40px 40px;
  animation: float-pattern 20s linear infinite;
}

@keyframes float-pattern {
  0% {
    transform: translateY(0px) translateX(0px);
  }
  25% {
    transform: translateY(-10px) translateX(5px);
  }
  50% {
    transform: translateY(0px) translateX(0px);
  }
  75% {
    transform: translateY(10px) translateX(-5px);
  }
  100% {
    transform: translateY(0px) translateX(0px);
  }
}

/* Auth card animations */
.auth-card {
  box-shadow: var(--shadow-large);
  transition: var(--transition-smooth);
}

.auth-card.login-active .login-panel {
  transform: translateX(0);
  opacity: 1;
}

.auth-card.login-active .signup-panel {
  transform: translateX(100%);
  opacity: 0;
}

.auth-card.signup-active .login-panel {
  transform: translateX(-100%);
  opacity: 0;
}

.auth-card.signup-active .signup-panel {
  transform: translateX(0);
  opacity: 1;
}

.auth-panel {
  transition: var(--transition-bounce);
}
.sliding-auth-container {
  position: relative;
  width: 850px;
  height: 550px;
  max-width: 90vw;
  background: hsl(var(--background));
  border-radius: 30px;
  box-shadow: 0 0 30px hsl(var(--foreground) / 0.1);
  overflow: hidden;
  margin: 20px;
}
/* Sliding Auth Styles */

.form-box {
  position: absolute;
  right: 0;
  width: 50%;
  height: 100%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  color: hsl(var(--foreground));
  text-align: center;
  padding: 30px 40px;
  z-index: 1;
  transition: 0.6s ease-in-out 1.2s, visibility 0s 1s;
  overflow-y: auto;
}

.sliding-auth-container.active .form-box {
  right: 50%;
}

.form-box.register {
  visibility: hidden;
}

.sliding-auth-container.active .form-box.register {
  visibility: visible;
}

.toggle-box {
  position: absolute;
  width: 100%;
  height: 100%;
    background: white
}

.toggle-box::before {
  content: "";
  position: absolute;
  left: -250%;
  width: 300%;
  height: 100%;
  background: var(--gradient-primary);
  border-radius: 150px;
  z-index: 2;
  transition: 1.8s ease-in-out;
}

.sliding-auth-container.active .toggle-box::before {
  left: 50%;
}

.toggle-panel {
  position: absolute;
  width: 50%;
  height: 100%;
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 2;
  transition: 0.6s ease-in-out;
  text-align: center;
}

.toggle-panel.toggle-left {
  left: 0;
  transition-delay: 1.2s;
    /* background-color: white; */
}

.sliding-auth-container.active .toggle-panel.toggle-left {
  left: -50%;
  transition-delay: 0.6s;
}

.toggle-panel.toggle-right {
  right: -50%;
  transition-delay: 0.6s;
}

.sliding-auth-container.active .toggle-panel.toggle-right {
  right: 0;
  transition-delay: 1.2s;
}

.toggle-btn {
  width: 160px;
  height: 46px;
  background: transparent;
  border: 2px solid white;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-btn:hover {
  background: white;
  color: hsl(var(--primary));
  transform: translateY(-2px);
}

/* Form styling overrides for sliding layout */
.form-box .space-y-6 {
  width: 100%;
  max-width: 320px;
  margin: 0 auto;
}

.form-box h2 {
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
}

/* Form elements */
.form-input {
  transition: var(--transition-fast);
  border: 2px solid hsl(var(--input-border));
  background: hsl(220 13% 96%) !important; /* Light gray background */
  color: hsl(var(--foreground)) !important;
}

.form-input::placeholder {
  color: hsl(220 13% 45%) !important; /* Dark gray for visibility */
  opacity: 1;
}

.form-input:focus {
  outline: none;
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 3px hsl(var(--ring) / 0.1);
  background: hsl(220 13% 98%) !important;
}

.form-input.error {
  border-color: hsl(var(--destructive));
  box-shadow: 0 0 0 3px hsl(var(--destructive) / 0.1);
}

/* Dark mode form inputs */
.dark .form-input {
  background: hsl(217.2 32.6% 17.5%) !important;
  color: hsl(var(--foreground)) !important;
}

.dark .form-input::placeholder {
  color: hsl(215 20.2% 65.1%) !important;
  opacity: 1;
}

.dark .form-input:focus {
  background: hsl(217.2 32.6% 20%) !important;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .sliding-auth-container {
    width: 100%;
    height: calc(100vh - 40px);
    border-radius: 20px;
    margin: 20px 0;
  }

  .form-box {
    bottom: 0;
    width: 100%;
    height: 70%;
    padding: 20px;
    overflow-y: auto;
  }

  .form-box .space-y-6 {
    max-width: 100%;
  }

  .sliding-auth-container.active .form-box {
    right: 0;
    bottom: 30%;
  }

  .toggle-box::before {
    left: 0;
    top: -270%;
    width: 100%;
    height: 300%;
    border-radius: 20vw;
  }

  .sliding-auth-container.active .toggle-box::before {
    left: 0;
    top: 70%;
  }

  .sliding-auth-container.active .toggle-panel.toggle-left {
    left: 0;
    top: -30%;
  }

  .toggle-panel {
    width: 100%;
    height: 30%;
  }

  .toggle-panel.toggle-left {
    top: 0;
  }

  .toggle-panel.toggle-right {
    right: 0;
    bottom: -30%;
  }

  .sliding-auth-container.active .toggle-panel.toggle-right {
    bottom: 0;
  }

  .toggle-panel h1 {
    font-size: 1.5rem;
  }

  .toggle-panel h2 {
    font-size: 1.5rem;
  }
}

@media screen and (max-width: 480px) {
  .form-box {
    padding: 20px;
  }

  .toggle-panel h1 {
    font-size: 1.25rem;
  }

  .toggle-panel h2 {
    font-size: 1.25rem;
  }
}

/* Button variants */
.btn-primary {
  background: var(--gradient-primary);
  color: hsl(var(--primary-foreground));
  box-shadow: var(--shadow-soft);
  transition: var(--transition-fast);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-primary:disabled {
  opacity: 0.6;
  transform: none;
  cursor: not-allowed;
}

/* Password strength meter */
.strength-meter {
  height: 4px;
  border-radius: 2px;
  overflow: hidden;
  background: hsl(var(--muted));
  transition: var(--transition-smooth);
}

.strength-bar {
  height: 100%;
  transition: width 0.3s ease, background-color 0.3s ease;
  border-radius: 2px;
}

.strength-weak {
  background: hsl(var(--destructive));
}
.strength-fair {
  background: hsl(var(--warning));
}
.strength-good {
  background: hsl(142 71% 55%);
}
.strength-strong {
  background: hsl(var(--success));
}

/* Validation checklist */
.validation-item {
  transition: var(--transition-fast);
  opacity: 0.5;
}

.validation-item.valid {
  opacity: 1;
  color: hsl(var(--success));
}

/* Loading spinner */
.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Shake animation for validation errors */
@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

.shake {
  animation: shake 0.3s ease-in-out;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .farm-background::before {
    animation: none;
  }

  .auth-panel,
  .form-input,
  .btn-primary,
  .strength-bar,
  .validation-item {
    transition: none;
  }

  .auth-card.login-active .login-panel,
  .auth-card.login-active .signup-panel,
  .auth-card.signup-active .login-panel,
  .auth-card.signup-active .signup-panel {
    transition: opacity 0.2s ease;
    transform: none;
  }
}



.container p {
  font-size: 14.5px;
  margin: 15px 0;
}

form {
  width: 100%;
}

.form-box {
  position: absolute;
  right: 0;
  width: 50%;
  height: 100%;
  background: #fff;
  display: flex;
  align-items: center;
  color: #333;
  text-align: center;
  padding: 40px;
  z-index: 1;
  transition: 0.6s ease-in-out 1.2s, visibility 0s 1s;
}

.container.active .form-box {
  right: 50%;
}

.form-box.register {
  visibility: hidden;
}
.container.active .form-box.register {
  visibility: visible;
}

.input-box {
  position: relative;
  margin: 30px 0;
}

.input-box input {
  width: 100%;
  padding: 13px 50px 13px 20px;
  background: #eee;
  border-radius: 8px;
  border: none;
  outline: none;
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.input-box input::placeholder {
  color: #888;
  font-weight: 400;
}

.input-box i {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20px;
}

.forgot-link {
  margin: -15px 0 15px;
}
.forgot-link a {
  font-size: 14.5px;
  color: #333;
}

.btn {
  width: 100%;
  height: 48px;
  background: rgba(58, 221, 118, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: #fff;
  font-weight: 600;
}

.social-icons {
  display: flex;
  justify-content: center;
}

.social-icons a {
  display: inline-flex;
  padding: 10px;
  border: 2px solid #ccc;
  border-radius: 8px;
  font-size: 24px;
  color: #333;
  margin: 0 8px;
}

.toggle-box {
  position: absolute;
  width: 100%;
  height: 100%;
}

.toggle-box::before {
  content: "";
  position: absolute;
  left: -250%;
  width: 300%;
  height: 100%;
  background: rgba(58, 221, 118, 1);
  /* border: 2px solid red; */
  border-radius: 150px;
  z-index: 2;
  transition: 1.8s ease-in-out;
}

.container.active .toggle-box::before {
  left: 50%;
}

.toggle-panel {
  position: absolute;
  width: 50%;
  height: 100%;
  /* background: seagreen; */
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 2;
  transition: 0.6s ease-in-out;
}

.toggle-panel.toggle-left {
  left: 0;
  transition-delay: 1.2s;
}
.container.active .toggle-panel.toggle-left {
  left: -50%;
  transition-delay: 0.6s;
}

.toggle-panel.toggle-right {
  right: -50%;
  transition-delay: 0.6s;
}
.container.active .toggle-panel.toggle-right {
  right: 0;
  transition-delay: 1.2s;
}

.toggle-panel p {
  margin-bottom: 20px;
}

.toggle-panel .btn {
  width: 160px;
  height: 46px;
  background: transparent;
  border: 2px solid #fff;
  box-shadow: none;
}

@media screen and (max-width: 650px) {
  .container {
    height: calc(100vh - 40px);
  }

  .form-box {
    bottom: 0;
    width: 100%;
    height: 70%;
  }

  .container.active .form-box {
    right: 0;
    bottom: 30%;
  }

  .toggle-box::before {
    left: 0;
    top: -270%;
    width: 100%;
    height: 300%;
    border-radius: 20vw;
  }

  .container.active .toggle-box::before {
    left: 0;
    top: 70%;
  }

  .container.active .toggle-panel.toggle-left {
    left: 0;
    top: -30%;
  }

  .toggle-panel {
    width: 100%;
    height: 30%;
  }
  .toggle-panel.toggle-left {
    top: 0;
  }
  .toggle-panel.toggle-right {
    right: 0;
    bottom: -30%;
  }

  .container.active .toggle-panel.toggle-right {
    bottom: 0;
  }
}

@media screen and (max-width: 400px) {
  .form-box {
    padding: 20px;
  }

  .toggle-panel h1 {
    font-size: 30px;
  }
}
