"use client";
import { useState } from "react";
import "./AuthStyle.css";
import { Sprout } from 'lucide-react';
import { LoginForm } from '@/components/auth/LoginForm';
import { SignupForm } from '@/components/auth/SignupForm';


export default function AuthPage() {
  const [isSignup, setIsSignup] = useState(false);


  return (
    <div className="min-h-screen min-w-screen farm-background flex items-center justify-center p-4">
      <div className={`sliding-auth-container ${isSignup ? 'active' : ''}`}>
        {/* Login Form */}
        <div className="form-box login">
          <LoginForm onToggleToSignup={() => setIsSignup(true)} />
        </div>

        {/* Signup Form */}
        <div className="form-box register">
          <SignupForm onToggleToLogin={() => setIsSignup(false)} />
        </div>

        {/* Toggle Box */}
        <div className="toggle-box ">
          <div className="toggle-panel toggle-left">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="p-2 rounded-full bg-white/20">
                <Sprout className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-3xl font-bold text-white">Farmti</h1>
            </div>
            <h2 className="text-2xl font-bold mb-4">Hello, Welcome!</h2>
            <p className="mb-6">Don't have an account?</p>
            <button 
              className="toggle-btn"
              onClick={() => setIsSignup(true)}
            >
              Sign Up
            </button>
          </div>

          <div className="toggle-panel toggle-right">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="p-2 rounded-full bg-white/20">
                <Sprout className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-3xl font-bold text-white">Farmti</h1>
            </div>
            <h2 className="text-2xl font-bold mb-4">Welcome Back!</h2>
            <p className="mb-6">Already have an account?</p>
            <button 
              className="toggle-btn"
              onClick={() => setIsSignup(false)}
            >
              Log In
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
