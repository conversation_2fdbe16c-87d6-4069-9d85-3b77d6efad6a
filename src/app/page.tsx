"use client";

import React, { useState, useEffect } from 'react';
import { 
  Droplets, 
  Zap, 
  TrendingUp, 
  Shield, 
  Smartphone,
  BarChart3,
  ArrowRight,
  CheckCircle,
  Star,
  Play,
  Menu,
  X
} from 'lucide-react';
import Link from 'next/link';

const LandingPage: React.FC = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const features = [
    {
      icon: <Droplets className="w-8 h-8 text-[#21c45d]" />,
      title: "Smart Water Management",
      description: "AI-powered irrigation systems that optimize water usage based on real-time soil conditions and weather data.",
      delay: "0ms"
    },
    {
      icon: <BarChart3 className="w-8 h-8 text-[#21c45d]" />,
      title: "Advanced Analytics", 
      description: "Get detailed insights into your farm's performance with comprehensive reports and predictive analytics.",
      delay: "100ms"
    },
    {
      icon: <Zap className="w-8 h-8 text-[#21c45d]" />,
      title: "Energy Efficient",
      description: "Reduce energy costs by up to 40% with our smart scheduling and automated control systems.",
      delay: "200ms"
    },
    {
      icon: <Smartphone className="w-8 h-8 text-[#21c45d]" />,
      title: "Mobile Control",
      description: "Monitor and control your entire farm from anywhere using our intuitive mobile application.",
      delay: "300ms"
    },
    {
      icon: <Shield className="w-8 h-8 text-[#21c45d]" />,
      title: "Reliable & Secure",
      description: "Enterprise-grade security with 99.9% uptime guarantee and 24/7 monitoring support.",
      delay: "400ms"
    },
    {
      icon: <TrendingUp className="w-8 h-8 text-[#21c45d]" />,
      title: "Increase Yield",
      description: "Boost crop yields by 25% on average with precision farming and optimal resource allocation.",
      delay: "500ms"
    }
  ];

  const testimonials = [
    {
      name: "Ostora Youssef",
      role: "Farm Manager", 
      company: "Green Valley Farms",
      rating: 5,
      text: "Farmti transformed our farming operations. We've seen 30% water savings and 20% yield increase in just 6 months."
    },
    {
      name: "Sarah Chen",
      role: "Agricultural Director",
      company: "Sustainable Crops Inc",
      rating: 5,
      text: "The analytics dashboard gives us insights we never had before. It's like having an agricultural expert 24/7."
    },
    {
      name: "Mohammed Al-Rashid",
      role: "Owner",
      company: "Desert Bloom Agriculture", 
      rating: 5,
      text: "In our arid climate, water conservation is critical. Farmti helped us reduce water usage by 45% while maintaining quality."
    }
  ];

  const stats = [
    { number: "10,000+", label: "Farms Managed" },
    { number: "40%", label: "Water Savings" },
    { number: "25%", label: "Yield Increase" },
    { number: "99.9%", label: "Uptime" }
  ];

  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      {/* Custom CSS for animations */}
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        @keyframes fadeInLeft {
          from {
            opacity: 0;
            transform: translateX(-30px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }
        
        @keyframes fadeInRight {
          from {
            opacity: 0;
            transform: translateX(30px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }
        
        @keyframes pulse {
          0%, 100% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.05);
          }
        }
        
        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }
        
        @keyframes gradient {
          0%, 100% {
            background-position: 0% 50%;
          }
          50% {
            background-position: 100% 50%;
          }
        }
        
        .animate-fadeInUp {
          animation: fadeInUp 0.8s ease-out forwards;
        }
        
        .animate-fadeInLeft {
          animation: fadeInLeft 0.8s ease-out forwards;
        }
        
        .animate-fadeInRight {
          animation: fadeInRight 0.8s ease-out forwards;
        }
        
        .animate-pulse-custom {
          animation: pulse 2s ease-in-out infinite;
        }
        
        .animate-float {
          animation: float 3s ease-in-out infinite;
        }
        
        .animate-gradient {
          background-size: 200% 200%;
          animation: gradient 3s ease infinite;
        }
        
        .feature-card {
          opacity: 0;
          transform: translateY(30px);
          transition: all 0.6s ease-out;
        }
        
        .feature-card.visible {
          opacity: 1;
          transform: translateY(0);
        }
        
        .parallax-bg {
          transform: translateY(${scrollY * 0.5}px);
        }
      `}</style>
      
      {/* Navigation */}
      <nav className="bg-white/95 backdrop-blur-md border-b border-gray-100 sticky top-0 z-50 transition-all duration-300">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex items-center space-x-3 animate-fadeInLeft">
              <div className="w-10 h-10 bg-gradient-to-br from-[#21c45d] to-[#1aa052] rounded-xl flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110">
                <Droplets className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-[#21c45d] to-[#1aa052] bg-clip-text text-transparent">Farmti</span>
            </div>

            {/* Desktop Menu */}
            <div className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-gray-600 hover:text-[#21c45d] font-medium transition-all duration-300 hover:scale-105 relative group">
                Features
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-[#21c45d] transition-all duration-300 group-hover:w-full"></span>
              </a>
              <a href="#how-it-works" className="text-gray-600 hover:text-[#21c45d] font-medium transition-all duration-300 hover:scale-105 relative group">
                How it Works
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-[#21c45d] transition-all duration-300 group-hover:w-full"></span>
              </a>
              <a href="#testimonials" className="text-gray-600 hover:text-[#21c45d] font-medium transition-all duration-300 hover:scale-105 relative group">
                Testimonials
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-[#21c45d] transition-all duration-300 group-hover:w-full"></span>
              </a>
              <a href="#pricing" className="text-gray-600 hover:text-[#21c45d] font-medium transition-all duration-300 hover:scale-105 relative group">
                Pricing
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-[#21c45d] transition-all duration-300 group-hover:w-full"></span>
              </a>
              <Link href="/dashboard">
                <button className="cursor-pointer bg-gradient-to-r from-[#21c45d] to-[#1aa052] text-white px-6 py-2 rounded-xl hover:shadow-lg transition-all duration-300 font-medium hover:scale-105 animate-gradient cursor-pointer">
                  Get Started
                </button>
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <div className="md:hidden">
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-all duration-300"
              >
                {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
              </button>
            </div>
          </div>

          {/* Mobile Menu */}
          {mobileMenuOpen && (
            <div className="md:hidden absolute top-16 left-0 right-0 bg-white border-b border-gray-100 shadow-lg animate-fadeInUp">
              <div className="px-4 py-6 space-y-4">
                <a href="#features" className="block text-gray-600 hover:text-[#21c45d] font-medium transition-colors">Features</a>
                <a href="#how-it-works" className="block text-gray-600 hover:text-[#21c45d] font-medium transition-colors">How it Works</a>
                <a href="#testimonials" className="block text-gray-600 hover:text-[#21c45d] font-medium transition-colors">Testimonials</a>
                <a href="#pricing" className="block text-gray-600 hover:text-[#21c45d] font-medium transition-colors">Pricing</a>
                <Link href="/dashboard">
                  <button className="cursor-pointer w-full bg-gradient-to-r from-[#21c45d] to-[#1aa052] text-white px-6 py-3 rounded-xl hover:shadow-lg transition-all duration-300 font-medium cursor-pointer">
                    Get Started
                  </button>
                </Link>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-green-50 via-white to-emerald-50">

<div 
  className="absolute inset-0 parallax-bg" 
  style={{
    backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2321c45d' fill-opacity='0.05'%3E%3Ccircle cx='7' cy='7' r='7'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
  }}
></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 relative">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="animate-fadeInLeft">
              <div className="inline-flex items-center px-4 py-2 bg-[#21c45d]/10 text-[#21c45d] rounded-full text-sm font-medium mb-8 hover:bg-[#21c45d]/20 transition-all duration-300 animate-pulse-custom">
                <Star className="w-4 h-4 mr-2" />
                Trusted by 10,000+ farms worldwide
              </div>
              
              <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
                Smart Farming
                <span className="bg-gradient-to-r from-[#21c45d] to-[#1aa052] bg-clip-text text-transparent animate-gradient"> Revolution</span>
              </h1>
              
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Transform your farm with AI-powered water management, real-time monitoring, and precision agriculture. 
                Increase yields while reducing costs and environmental impact.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 mb-12">
                <Link href="/dashboard">
                  <button className="cursor-pointer bg-gradient-to-r from-[#21c45d] to-[#1aa052] text-white px-8 py-4 rounded-xl hover:shadow-xl transition-all duration-300 font-semibold text-lg cursor-pointer hover:scale-105 animate-gradient group">
                    Start Free Trial
                    <ArrowRight className="w-5 h-5 ml-2 inline transition-transform duration-300 group-hover:translate-x-1" />
                  </button>
                </Link>
                
                <button className="cursor-pointer flex items-center justify-center px-8 py-4 border border-gray-200 rounded-xl hover:bg-gray-50 transition-all duration-300 font-semibold text-lg hover:scale-105 group">
                  <Play className="w-5 h-5 mr-2 transition-transform duration-300 group-hover:scale-110" />
                  Watch Demo
                </button>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
                {stats.map((stat, index) => (
                  <div key={index} className="text-center group hover:scale-110 transition-transform duration-300">
                    <div className="text-3xl font-bold bg-gradient-to-r from-[#21c45d] to-[#1aa052] bg-clip-text text-transparent mb-1 group-hover:animate-pulse">{stat.number}</div>
                    <div className="text-gray-600 text-sm">{stat.label}</div>
                  </div>
                ))}
              </div>
            </div>

            <div className="relative animate-fadeInRight">
              <div className="bg-white rounded-3xl shadow-2xl p-8 transform hover:rotate-0 transition-all duration-500 hover:shadow-3xl animate-float">
                <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-6 mb-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-bold text-gray-900">Farm Overview</h3>
                    <div className="w-3 h-3 bg-[#21c45d] rounded-full animate-pulse"></div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Water Usage</span>
                      <span className="font-semibold">40,622L</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-gradient-to-r from-[#21c45d] to-[#1aa052] h-2 rounded-full w-3/4 animate-pulse"></div>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Soil Moisture</span>
                      <span className="font-semibold text-[#21c45d]">76%</span>
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-[#21c45d]/10 rounded-xl p-4 text-center hover:bg-[#21c45d]/20 transition-colors duration-300">
                    <div className="text-2xl font-bold text-[#21c45d]">+25%</div>
                    <div className="text-[#1aa052] text-sm">Yield Increase</div>
                  </div>
                  <div className="bg-[#21c45d]/10 rounded-xl p-4 text-center hover:bg-[#21c45d]/20 transition-colors duration-300">
                    <div className="text-2xl font-bold text-[#21c45d]">-40%</div>
                    <div className="text-[#1aa052] text-sm">Water Saved</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      {/* Updated Features Section - Simple CSS Animation */}
<section id="features" className="py-20 bg-gray-50 relative overflow-hidden">
  <div className="absolute inset-0 bg-gradient-to-br from-transparent via-[#21c45d]/5 to-transparent"></div>
  <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
    <div className="text-center mb-16 animate-fadeInUp">
      <h2 className="text-4xl font-bold text-gray-900 mb-4">
        Everything You Need for Smart Farming
      </h2>
      <p className="text-xl text-gray-600 max-w-3xl mx-auto">
        Our comprehensive platform combines cutting-edge technology with practical farming solutions 
        to help you optimize every aspect of your agricultural operations.
      </p>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {features.map((feature, index) => (
        <div 
          key={index}
          className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 hover:shadow-xl hover:border-[#21c45d]/30 transition-all duration-500 group hover:scale-105 opacity-0 animate-fadeInUp"
          style={{ 
            animationDelay: `${(index * 150) + 500}ms`,
            animationFillMode: 'forwards'
          }}
        >
          <div className="mb-6 group-hover:scale-110 transition-transform duration-300 group-hover:animate-pulse">
            {feature.icon}
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-[#21c45d] transition-colors duration-300">
            {feature.title}
          </h3>
          <p className="text-gray-600 leading-relaxed">{feature.description}</p>
        </div>
      ))}
    </div>
  </div>
</section>

      {/* How It Works Section */}
      <section id="how-it-works" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fadeInUp">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Simple Setup, Powerful Results
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Get started with Farmti in just three easy steps and start seeing results immediately.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                step: "01",
                title: "Install Sensors",
                description: "Our team installs smart sensors across your farm to monitor soil moisture, weather conditions, and plant health in real-time."
              },
              {
                step: "02", 
                title: "Connect & Configure",
                description: "Connect your irrigation systems to our platform and configure automated schedules based on your crops and growing conditions."
              },
              {
                step: "03",
                title: "Monitor & Optimize", 
                description: "Use our dashboard to monitor performance, receive alerts, and continuously optimize your farming operations for maximum efficiency."
              }
            ].map((step, index) => (
              <div key={index} className="relative text-center animate-fadeInUp group" style={{ animationDelay: `${index * 200}ms` }}>
                <div className="bg-gradient-to-br from-[#21c45d] to-[#1aa052] text-white rounded-full w-16 h-16 flex items-center justify-center text-xl font-bold mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 animate-gradient">
                  {step.step}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-[#21c45d] transition-colors duration-300">{step.title}</h3>
                <p className="text-gray-600">{step.description}</p>
                
                {index < 2 && (
                  <div className="hidden md:block absolute top-8 left-full w-full">
                    <ArrowRight className="w-6 h-6 text-[#21c45d] mx-auto animate-pulse" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fadeInUp">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Trusted by Farmers Worldwide
            </h2>
            <p className="text-xl text-gray-600">
              See what our customers have to say about their Farmti experience.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div 
                key={index}
                className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 hover:shadow-xl hover:border-[#21c45d]/30 transition-all duration-500 group hover:scale-105 animate-fadeInUp"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-[#21c45d] fill-current group-hover:animate-pulse" style={{ animationDelay: `${i * 100}ms` }} />
                  ))}
                </div>
                <p className="text-gray-700 mb-6 italic">"{testimonial.text}"</p>
                <div className="border-t pt-4">
                  <div className="font-semibold text-gray-900 group-hover:text-[#21c45d] transition-colors duration-300">{testimonial.name}</div>
                  <div className="text-gray-600 text-sm">{testimonial.role}</div>
                  <div className="text-[#21c45d] text-sm">{testimonial.company}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-[#21c45d] to-[#1aa052] animate-gradient relative overflow-hidden">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8 relative">
          <h2 className="text-4xl font-bold text-white mb-6 animate-fadeInUp">
            Ready to Transform Your Farm?
          </h2>
          <p className="text-xl text-green-100 mb-8 leading-relaxed animate-fadeInUp" style={{ animationDelay: '200ms' }}>
            Join thousands of farmers who have already revolutionized their operations with Farmti. 
            Start your free trial today and see the difference smart farming can make.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fadeInUp" style={{ animationDelay: '400ms' }}>
            <Link href="/dashboard">
              <button className="cursor-pointer bg-white text-[#21c45d] px-8 py-4 rounded-xl hover:bg-gray-100 transition-all duration-300 font-semibold text-lg cursor-pointer hover:scale-105 group">
                Start Free Trial
                <ArrowRight className="w-5 h-5 ml-2 inline transition-transform duration-300 group-hover:translate-x-1" />
              </button>
            </Link>
            <button className="cursor-pointer border border-white text-white px-8 py-4 rounded-xl hover:bg-white/10 transition-all duration-300 font-semibold text-lg hover:scale-105">
              Schedule Demo
            </button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-3 mb-6 animate-fadeInLeft">
                <div className="w-10 h-10 bg-gradient-to-br from-[#21c45d] to-[#1aa052] rounded-xl flex items-center justify-center">
                  <Droplets className="w-6 h-6 text-white" />
                </div>
                <span className="text-2xl font-bold">Farmti</span>
              </div>
              <p className="text-gray-400 mb-6 max-w-md">
                Revolutionizing agriculture with smart water management and precision farming technology. 
                Building a sustainable future, one farm at a time.
              </p>
              <div className="flex space-x-4">
                {['f', 't', 'in'].map((social, index) => (
                  <div key={social} className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-[#21c45d] transition-all duration-300 cursor-pointer hover:scale-110" style={{ animationDelay: `${index * 100}ms` }}>
                    <span className="text-sm">{social}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="animate-fadeInUp">
              <h3 className="font-semibold mb-4 text-[#21c45d]">Product</h3>
              <ul className="space-y-3 text-gray-400">
                <li><a href="#" className="hover:text-[#21c45d] transition-colors">Features</a></li>
                <li><a href="#" className="hover:text-[#21c45d] transition-colors">Pricing</a></li>
                <li><a href="#" className="hover:text-[#21c45d] transition-colors">API</a></li>
                <li><a href="#" className="hover:text-[#21c45d] transition-colors">Documentation</a></li>
              </ul>
            </div>

            <div className="animate-fadeInUp" style={{ animationDelay: '200ms' }}>
              <h3 className="font-semibold mb-4 text-[#21c45d]">Company</h3>
              <ul className="space-y-3 text-gray-400">
                <li><a href="#" className="hover:text-[#21c45d] transition-colors">About</a></li>
                <li><a href="#" className="hover:text-[#21c45d] transition-colors">Blog</a></li>
                <li><a href="#" className="hover:text-[#21c45d] transition-colors">Careers</a></li>
                <li><a href="#" className="hover:text-[#21c45d] transition-colors">Contact</a></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 Farmti. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-[#21c45d] text-sm transition-colors">Privacy Policy</a>
              <a href="#" className="text-gray-400 hover:text-[#21c45d] text-sm transition-colors">Terms of Service</a>
              <a href="#" className="text-gray-400 hover:text-[#21c45d] text-sm transition-colors">Cookie Policy</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
