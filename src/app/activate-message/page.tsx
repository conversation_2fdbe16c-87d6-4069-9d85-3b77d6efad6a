"use client"
import React, { useState, useEffect } from 'react';
import { useSearchParams } from "next/navigation";
import {useRouter} from "next/navigation";




// function onBackToLogin()
// {
//   router.push("/Auth");
// }
const FarmtiCheckEmailPage = ({ 
//   userEmail = "<EMAIL>",
  onResendEmail = () => console.log("Resending email..."),
  // onBackToLogin = () => console.log("Redirecting to login...")
  // onBackToLogin = () => {const router = useRouter(); router.push("/Auth")}
}) => {
  const router = useRouter(); // ✅ hook at the top
  
  function onBackToLogin() {
    router.push("/auth"); // ✅ safe to use here
  }
    const searchParams = useSearchParams();
    const userEmail = searchParams.get("email");
  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes countdown
  const [isResending, setIsResending] = useState(false);
  const [emailResent, setEmailResent] = useState(false);

  // Countdown timer effect
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [timeLeft]);

  const formatTime = (seconds:any) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleResendEmail = async () => {
    setIsResending(true);
    try {
      await onResendEmail();
      setEmailResent(true);
      setTimeLeft(300); // Reset timer
      setTimeout(() => setEmailResent(false), 3000);
    } catch (error) {
      console.error("Failed to resend email:", error);
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-green-100 flex items-center justify-center p-8">
      <div className="max-w-4xl w-full">
        {/* Main Card */}
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl p-12 border border-green-200">
          
          {/* Header Section */}
          <div className="text-center mb-12">
            {/* Farmti Logo */}
            <div className="mb-8">
              <h1 className="text-6xl font-bold text-green-800 tracking-wider mb-3">
                🌾 FARMTI
              </h1>
              <p className="text-green-600 text-lg font-medium">
                Smart Farm Management Platform
              </p>
            </div>

            {/* Email Icon Animation */}
            <div className="relative mb-8">
              <div className="w-32 h-32 mx-auto bg-gradient-to-br from-green-100 to-green-200 rounded-full flex items-center justify-center shadow-lg">
                <div className="text-6xl animate-bounce">📧</div>
              </div>
              {/* Animated check circle */}
              <div className="absolute -top-3 -right-3 w-12 h-12 bg-green-500 rounded-full flex items-center justify-center animate-pulse">
                <span className="text-white text-2xl">✓</span>
              </div>
            </div>

            {/* Main Message */}
            <h2 className="text-4xl font-bold text-green-900 mb-4">
              Check Your Email! 📮
            </h2>
            <p className="text-green-700 text-xl mb-3">
              We've sent a confirmation link to:
            </p>
            <p className="text-green-800 font-semibold text-xl bg-green-100 px-6 py-3 rounded-lg border border-green-300 inline-block">
              {userEmail}
            </p>
          </div>

          {/* Instructions and Content Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-10">
            
            {/* Left Column - Instructions */}
            <div>
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-8 border border-green-200">
                <h3 className="text-green-900 font-semibold text-xl mb-6 flex items-center">
                  <span className="text-2xl mr-3">🌱</span>
                  Next Steps to Activate Your Farm:
                </h3>
                <div className="space-y-4">
                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0">1</div>
                    <p className="text-green-800 text-base">
                      Open your email inbox and look for our confirmation message
                    </p>
                  </div>
                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0">2</div>
                    <p className="text-green-800 text-base">
                      Click the "Confirm Email & Start Farming" button
                    </p>
                  </div>
                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0">3</div>
                    <p className="text-green-800 text-base">
                      Start managing your farm with IoT sensors and smart analytics
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - IoT Features Preview */}
            <div>
              <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-xl p-8 border border-blue-200">
                <h3 className="text-green-900 font-semibold text-xl mb-6 text-center">
                  🚜 What's Waiting for You:
                </h3>
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center p-4 bg-white/60 rounded-lg">
                    <div className="text-4xl mb-3">💧</div>
                    <div className="text-sm text-green-700 font-medium">Smart Irrigation</div>
                    <div className="text-xs text-green-600 mt-1">Automated water management</div>
                  </div>
                  <div className="text-center p-4 bg-white/60 rounded-lg">
                    <div className="text-4xl mb-3">🌡️</div>
                    <div className="text-sm text-green-700 font-medium">Climate Monitoring</div>
                    <div className="text-xs text-green-600 mt-1">Temperature & humidity tracking</div>
                  </div>
                  <div className="text-center p-4 bg-white/60 rounded-lg">
                    <div className="text-4xl mb-3">📊</div>
                    <div className="text-sm text-green-700 font-medium">Real-time Analytics</div>
                    <div className="text-xs text-green-600 mt-1">Data-driven insights</div>
                  </div>
                  <div className="text-center p-4 bg-white/60 rounded-lg">
                    <div className="text-4xl mb-3">🛰️</div>
                    <div className="text-sm text-green-700 font-medium">IoT Sensors</div>
                    <div className="text-xs text-green-600 mt-1">Connected farm monitoring</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Timer and Actions Row */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            
            {/* Timer Section */}
            <div className="text-center">
              {timeLeft > 0 ? (
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
                  <p className="text-orange-800 text-base mb-3">
                    ⏰ Link expires in: 
                  </p>
                  <div className="text-4xl font-bold text-orange-900 mb-3">
                    {formatTime(timeLeft)}
                  </div>
                  <p className="text-orange-600 text-sm">
                    Don't see the email? Check your spam/junk folder
                  </p>
                </div>
              ) : (
                <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                  <p className="text-red-800 text-base">
                    ⚠️ Your confirmation link has expired
                  </p>
                </div>
              )}
            </div>

            {/* Resend Section */}
            <div className="text-center">
              {emailResent && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                  <p className="text-green-800 text-base">
                    ✅ New confirmation email sent successfully!
                  </p>
                </div>
              )}

              <button
                onClick={handleResendEmail}
                disabled={isResending || (timeLeft > 240)}
                className={`
                  w-full py-4 px-8 rounded-xl font-semibold text-base transition-all duration-300
                  ${isResending || (timeLeft > 240)
                    ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                    : 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-lg hover:shadow-xl transform hover:scale-105'
                  }
                `}
              >
                {isResending ? (
                  <div className="flex items-center justify-center space-x-3">
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-gray-400 border-t-transparent"></div>
                    <span>Sending Email...</span>
                  </div>
                ) : timeLeft > 240 ? (
                  `Resend Available in ${formatTime(timeLeft - 240)}`
                ) : (
                  '📧 Resend Confirmation Email'
                )}
              </button>
            </div>
          </div>

          {/* Footer Actions */}
          <div className="text-center space-y-4">
            <button
              onClick={onBackToLogin}
              className="text-green-600 hover:text-green-800 underline text-base font-medium transition-colors duration-200"
            >
              ← Back to Login Page
            </button>
            
            <div className="text-sm text-green-600">
              <p>Need help? Contact our support team</p>
              <p className="font-medium text-base"><EMAIL></p>
            </div>
          </div>
        </div>

        {/* Bottom Information */}
        <div className="text-center mt-8">
          <p className="text-green-600 text-base">
            🔒 Your data is secure and protected with Farmti
          </p>
        </div>
      </div>
    </div>
  );
};

export default FarmtiCheckEmailPage;