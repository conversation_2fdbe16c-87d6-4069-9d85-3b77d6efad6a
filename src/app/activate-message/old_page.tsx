"use client"
import React, { useState, useEffect } from 'react';
import { useSearchParams } from "next/navigation";
// const searchParams = useSearchParams();
// const userEmail = searchParams.get("email");


const FarmtiCheckEmailPage = ({ 
  // userEmail = "<EMAIL>",
  onResendEmail = () => console.log("Resending email..."),
  onBackToLogin = () => console.log("Redirecting to login...")
}) => {
  const searchParams = useSearchParams();
  const userEmail = searchParams.get("email");
  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes countdown
  const [isResending, setIsResending] = useState(false);
  const [emailResent, setEmailResent] = useState(false);

  // Countdown timer effect
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [timeLeft]);

  const formatTime = (seconds:any) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleResendEmail = async () => {
    setIsResending(true);
    try {
      await onResendEmail();
      setEmailResent(true);
      setTimeLeft(300); // Reset timer
      setTimeout(() => setEmailResent(false), 3000);
    } catch (error) {
      console.error("Failed to resend email:", error);
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-green-100 flex items-center justify-center p-4">
      <div className="max-w-lg w-full">
        {/* Main Card */}
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl p-8 border border-green-200">
          
          {/* Header Section */}
          <div className="text-center mb-8">
            {/* Farmti Logo */}
            <div className="mb-6">
              <h1 className="text-4xl font-bold text-green-800 tracking-wider mb-2">
                🌾 FARMTI
              </h1>
              <p className="text-green-600 text-sm font-medium">
                Smart Farm Management Platform
              </p>
            </div>

            {/* Email Icon Animation */}
            <div className="relative mb-6">
              <div className="w-24 h-24 mx-auto bg-gradient-to-br from-green-100 to-green-200 rounded-full flex items-center justify-center shadow-lg">
                <div className="text-4xl animate-bounce">📧</div>
              </div>
              {/* Animated check circle */}
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center animate-pulse">
                <span className="text-white text-lg">✓</span>
              </div>
            </div>

            {/* Main Message */}
            <h2 className="text-2xl font-bold text-green-900 mb-3">
              Check Your Email! 📮
            </h2>
            <p className="text-green-700 text-lg mb-2">
              We've sent a confirmation link to:
            </p>
            <p className="text-green-800 font-semibold text-lg bg-green-100 px-4 py-2 rounded-lg border border-green-300">
              {userEmail}
            </p>
          </div>

          {/* Instructions */}
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 mb-6 border border-green-200">
            <h3 className="text-green-900 font-semibold mb-4 flex items-center">
              <span className="text-xl mr-2">🌱</span>
              Next Steps to Activate Your Farm:
            </h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <span className="text-green-600 font-bold">1.</span>
                <p className="text-green-800 text-sm">
                  Open your email inbox and look for our confirmation message
                </p>
              </div>
              <div className="flex items-start space-x-3">
                <span className="text-green-600 font-bold">2.</span>
                <p className="text-green-800 text-sm">
                  Click the "Confirm Email & Start Farming" button
                </p>
              </div>
              <div className="flex items-start space-x-3">
                <span className="text-green-600 font-bold">3.</span>
                <p className="text-green-800 text-sm">
                  Start managing your farm with IoT sensors and smart analytics
                </p>
              </div>
            </div>
          </div>

          {/* Timer and Resend Section */}
          <div className="text-center mb-6">
            {timeLeft > 0 ? (
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
                <p className="text-orange-800 text-sm mb-2">
                  ⏰ Link expires in: 
                  <span className="font-bold text-lg ml-2 text-orange-900">
                    {formatTime(timeLeft)}
                  </span>
                </p>
                <p className="text-orange-600 text-xs">
                  Don't see the email? Check your spam/junk folder
                </p>
              </div>
            ) : (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                <p className="text-red-800 text-sm">
                  ⚠️ Your confirmation link has expired
                </p>
              </div>
            )}

            {/* Resend Button */}
            {emailResent && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
                <p className="text-green-800 text-sm">
                  ✅ New confirmation email sent successfully!
                </p>
              </div>
            )}

            <button
              onClick={handleResendEmail}
              disabled={isResending || (timeLeft > 240)} // Disable if recently sent
              className={`
                w-full py-3 px-6 rounded-xl font-semibold text-sm transition-all duration-300
                ${isResending || (timeLeft > 240)
                  ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                  : 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-lg hover:shadow-xl transform hover:scale-105'
                }
              `}
            >
              {isResending ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-400 border-t-transparent"></div>
                  <span>Sending Email...</span>
                </div>
              ) : timeLeft > 240 ? (
                `Resend Available in ${formatTime(timeLeft - 240)}`
              ) : (
                '📧 Resend Confirmation Email'
              )}
            </button>
          </div>

          {/* IoT Features Preview */}
          <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-xl p-6 mb-6 border border-blue-200">
            <h3 className="text-green-900 font-semibold mb-4 text-center">
              🚜 What's Waiting for You:
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl mb-2">💧</div>
                <div className="text-xs text-green-700 font-medium">Smart Irrigation</div>
              </div>
              <div className="text-center">
                <div className="text-2xl mb-2">🌡️</div>
                <div className="text-xs text-green-700 font-medium">Climate Monitoring</div>
              </div>
              <div className="text-center">
                <div className="text-2xl mb-2">📊</div>
                <div className="text-xs text-green-700 font-medium">Real-time Analytics</div>
              </div>
              <div className="text-center">
                <div className="text-2xl mb-2">🛰️</div>
                <div className="text-xs text-green-700 font-medium">IoT Sensors</div>
              </div>
            </div>
          </div>

          {/* Footer Actions */}
          <div className="text-center space-y-3">
            <button
              onClick={onBackToLogin}
              className="text-green-600 hover:text-green-800 underline text-sm font-medium transition-colors duration-200"
            >
              ← Back to Login Page
            </button>
            
            <div className="text-xs text-green-600">
              <p>Need help? Contact our support team</p>
              <p className="font-medium"><EMAIL></p>
            </div>
          </div>
        </div>

        {/* Bottom Information */}
        <div className="text-center mt-6">
          <p className="text-green-600 text-sm">
            🔒 Your data is secure and protected with Farmti
          </p>
        </div>
      </div>
    </div>
  );
};

export default FarmtiCheckEmailPage;