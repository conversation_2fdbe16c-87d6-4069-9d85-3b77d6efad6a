"use client"

import React, { useState } from 'react';

// Define interfaces for type safety
interface ErrorDetails {
  title: string;
  icon: string;
  color: 'red' | 'yellow' | 'blue';
  description: string;
}

interface ColorScheme {
  bg: string;
  cardBg: string;
  border: string;
  statusBg: string;
  statusText: string;
  messageText: string;
  titleText: string;
}

interface FarmtiErrorPageProps {
  statusCode?: number;
  message?: string;
  onRetry?: (() => void) | (() => Promise<void>) | null | undefined;
  onGoHome?: () => void;
  onContactSupport?: () => void;
  showRetry?: boolean;
  showGoHome?: boolean;
  showContactSupport?: boolean;
}

const FarmtiErrorPage: React.FC<FarmtiErrorPageProps> = ({
  statusCode = 400,         
  message = "Invalid email format. Please provide a valid email address.",
  onRetry = null,
  onGoHome = () => window.location.href = '/Auth',
  onContactSupport = null,
  showRetry = true,
  showGoHome = true,
  showContactSupport = true
}) => {
  const [isRetrying, setIsRetrying] = useState<boolean>(false);

  // Handle retry functionality
  const handleRetry = async (): Promise<void> => {
    if (!onRetry || isRetrying) return;
    
    setIsRetrying(true);
    try {
      await onRetry();
    } catch (retryError) {
      console.error('Retry failed:', retryError);
    } finally {
      setIsRetrying(false);
    }
  };

  // Get status code specific information with dynamic icons
  const getErrorDetails = (code: number): ErrorDetails => {
    const errorDetailsMap: Record<number, ErrorDetails> = {
      400: {
        title: "Invalid Request",
        icon: "⚠️",
        color: "yellow",
        description: "The request could not be processed due to invalid parameters."
      },
      401: {
        title: "Authentication Required",
        icon: "🔐",
        color: "red",
        description: "You need to log in to access your farm dashboard."
      },
      403: {
        title: "Access Forbidden",
        icon: "🚫",
        color: "red",
        description: "You don't have permission to access this resource."
      },
      404: {
        title: "Resource Not Found",
        icon: "🔍",
        color: "blue",
        description: "The requested farm data or page could not be found."
      },
      408: {
        title: "Request Timeout",
        icon: "⏰",
        color: "yellow",
        description: "The connection to farm sensors timed out."
      },
      422: {
        title: "Validation Error",
        icon: "📝",
        color: "yellow",
        description: "The submitted data failed validation checks."
      },
      429: {
        title: "Too Many Requests",
        icon: "🚦",
        color: "yellow",
        description: "Please wait a moment before trying again."
      },
      500: {
        title: "Server Error",
        icon: "🛠️",
        color: "red",
        description: "Our farm management system is experiencing technical difficulties."
      },
      502: {
        title: "Service Unavailable",
        icon: "📡",
        color: "red",
        description: "Farm monitoring services are temporarily unavailable."
      },
      503: {
        title: "Maintenance Mode",
        icon: "🔧",
        color: "blue",
        description: "Farmti is currently undergoing maintenance."
      }
    };

    return errorDetailsMap[code] || {
      title: "Unexpected Error",
      icon: "❌",
      color: "red",
      description: "Something went wrong with your request."
    };
  };

  const errorDetails: ErrorDetails = getErrorDetails(statusCode);

  // Color schemes based on error type
  const colorSchemes: Record<string, ColorScheme> = {
    red: {
      bg: "from-red-50 via-red-50 to-red-100",
      cardBg: "from-red-50 to-red-100",
      border: "border-red-200",
      statusBg: "bg-red-600",
      statusText: "text-white",
      messageText: "text-red-800",
      titleText: "text-red-900"
    },
    yellow: {
      bg: "from-yellow-50 via-orange-50 to-yellow-100",
      cardBg: "from-yellow-50 to-orange-100",
      border: "border-yellow-200",
      statusBg: "bg-yellow-600",
      statusText: "text-white",
      messageText: "text-yellow-800",
      titleText: "text-yellow-900"
    },
    blue: {
      bg: "from-blue-50 via-blue-50 to-blue-100",
      cardBg: "from-blue-50 to-blue-100",
      border: "border-blue-200",
      statusBg: "bg-blue-600",
      statusText: "text-white",
      messageText: "text-blue-800",
      titleText: "text-blue-900"
    }
  };

  const colors: ColorScheme = colorSchemes[errorDetails.color];

  return (
    <div className={`min-h-screen bg-gradient-to-br ${colors.bg} flex items-center justify-center p-6`}>
      <div className="max-w-2xl w-full">
        
        {/* Main Error Card */}
        <div className={`bg-gradient-to-br ${colors.cardBg} rounded-2xl shadow-2xl p-8 border-2 ${colors.border} backdrop-blur-sm`}>
          
          {/* Farmti Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-green-800 tracking-wider mb-2">
              🌾 FARMTI
            </h1>
            <p className="text-green-600 text-lg font-medium">
              Smart Farm Management Platform
            </p>
          </div>

          {/* Error Icon and Status Code */}
          <div className="text-center mb-8">
            <div className="relative inline-block mb-6">
              <div className="w-32 h-32 bg-white/60 rounded-full flex items-center justify-center shadow-lg border-4 border-white/80">
                <div className="text-6xl">
                  {errorDetails.icon}
                </div>
              </div>
              <div className={`absolute -bottom-2 -right-2 ${colors.statusBg} ${colors.statusText} px-4 py-2 rounded-full font-bold text-lg shadow-lg`}>
                {statusCode}
              </div>
            </div>
            
            <h2 className={`text-3xl font-bold ${colors.titleText} mb-3`}>
              {errorDetails.title}
            </h2>
            <p className="text-gray-600 text-lg mb-4">
              {errorDetails.description}
            </p>
          </div>

          {/* Error Message */}
          <div className="bg-white/60 rounded-xl p-6 mb-8 border border-white/50 text-center">
            <h3 className="font-semibold text-gray-800 mb-4 flex items-center justify-center">
              <span className="mr-2">💬</span>
              API Response
            </h3>
            <div className="space-y-4">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-2">Status Code</p>
                <p className={`${colors.statusBg} ${colors.statusText} inline-block px-4 py-2 rounded-lg font-bold text-lg`}>
                  {statusCode}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 mb-2">Message</p>
                <p className={`${colors.messageText} text-lg leading-relaxed max-w-md mx-auto`}>
                  {message}
                </p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap justify-center gap-4 mb-6">
            
            {showRetry && onRetry && (
              <button
                onClick={handleRetry}
                disabled={isRetrying}
                className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-semibold px-6 py-3 rounded-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center space-x-3 shadow-lg"
                type="button"
              >
                {isRetrying ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                    <span>Retrying...</span>
                  </>
                ) : (
                  <>
                    <span className="text-xl">🔄</span>
                    <span>Try Again</span>
                  </>
                )}
              </button>
            )}

            {showGoHome && (
              <button
                onClick={onGoHome}
                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold px-6 py-3 rounded-xl transition-all duration-200 transform hover:scale-105 flex items-center space-x-3 shadow-lg"
                type="button"
              >
                <span className="text-xl">🏠</span>
                <span>Go to Home</span>
              </button>
            )}

            {showContactSupport && onContactSupport && (
              <button
                onClick={onContactSupport}
                className="bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-semibold px-6 py-3 rounded-xl transition-all duration-200 transform hover:scale-105 flex items-center space-x-3 shadow-lg"
                type="button"
              >
                <span className="text-xl">💬</span>
                <span>Contact Support</span>
              </button>
            )}
          </div>

          {/* Additional Actions - Removed refresh button */}

          {/* IoT Status Indicators */}
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-200">
            <h3 className="text-green-900 font-semibold text-lg mb-4 text-center flex items-center justify-center">
              <span className="mr-2">🌱</span>
              Farm System Status
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-3 bg-white/60 rounded-lg">
                <div className="text-2xl mb-2">💧</div>
                <div className="text-xs text-green-700 font-medium">Irrigation</div>
                <div className="text-xs text-green-600">Monitoring</div>
              </div>
              <div className="text-center p-3 bg-white/60 rounded-lg">
                <div className="text-2xl mb-2">🌡️</div>
                <div className="text-xs text-green-700 font-medium">Climate</div>
                <div className="text-xs text-green-600">Active</div>
              </div>
              <div className="text-center p-3 bg-white/60 rounded-lg">
                <div className="text-2xl mb-2">📊</div>
                <div className="text-xs text-green-700 font-medium">Analytics</div>
                <div className="text-xs text-green-600">Available</div>
              </div>
              <div className="text-center p-3 bg-white/60 rounded-lg">
                <div className="text-2xl mb-2">🛰️</div>
                <div className="text-xs text-green-700 font-medium">Sensors</div>
                <div className="text-xs text-green-600">Connected</div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-6">
          <p className="text-green-600 text-sm flex items-center justify-center space-x-1">
            <span>🚜</span>
            <span>We're working hard to keep your farm management system running smoothly!</span>
          </p>
          <p className="text-gray-500 text-xs mt-2">
            Error ID: {Date.now().toString(36).toUpperCase()} • {new Date().toLocaleString()}
          </p>
        </div>
      </div>
    </div>
  );
};

export default FarmtiErrorPage;