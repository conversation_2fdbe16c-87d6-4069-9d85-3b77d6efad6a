"use client";

import React, { useMemo, useState } from "react";
import {
  Droplets,
  Plus,
  Download,
  Upload,
  Factory,
  Gauge,
  Edit3,
  Trash2,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from "lucide-react";

/* ---------------- Types ---------------- */
type SourceType = "well" | "reservoir" | "municipal" | "rainwater";
type SourceStatus = "online" | "offline" | "maintenance";

type Source = {
  id: string;
  name: string;
  type: SourceType;
  location: string;         // free text for now
  capacityM3?: number;      // for tanks/reservoirs
  maxFlowLpm?: number;      // physical limit
  status: SourceStatus;
};

type Meter = {
  id: string;
  sourceId: string;
  label: string;
  serial: string;
  unit: "L" | "m3";
  kFactor: number;          // pulses per unit
  lastReading: number;      // counter units
  installedAt: string;      // ISO date
};

type CostTier = {
  id: string;
  from: number;             // inclusive m3
  to?: number | null;       // exclusive m3, null = infinity
  pricePerM3: number;       // currency/unit
};

const PRIMARY = "#21c45d";

/* ---------------- UI atoms ---------------- */
const Card: React.FC<{ title?: string; icon?: React.ReactNode; children: React.ReactNode; className?: string }> = ({ title, icon, children, className }) => (
  <div className={`bg-white rounded-2xl border border-gray-100 shadow-sm ${className || ""}`}>
    {title && (
      <div className="px-5 py-4 border-b border-gray-100 flex items-center gap-2">
        {icon}
        <h3 className="text-sm font-semibold text-gray-900">{title}</h3>
      </div>
    )}
    <div className="p-5">{children}</div>
  </div>
);

const Input = (p: React.InputHTMLAttributes<HTMLInputElement>) => (
  <input
    {...p}
    className="w-full rounded-xl border border-gray-200 bg-white px-3 py-2 text-sm text-gray-900 outline-none focus:ring-2"
  />
);

const Select = (p: React.SelectHTMLAttributes<HTMLSelectElement>) => (
  <select
    {...p}
    className="w-full rounded-xl border border-gray-200 bg-white px-3 py-2 text-sm text-gray-900 outline-none focus:ring-2"
  />
);

const PrimaryBtn = (p: React.ButtonHTMLAttributes<HTMLButtonElement>) => (
  <button
    {...p}
    className="inline-flex items-center gap-2 rounded-xl px-4 py-2 text-sm font-semibold text-white"
    style={{ background: PRIMARY }}
  />
);

const GhostBtn = (p: React.ButtonHTMLAttributes<HTMLButtonElement>) => (
  <button
    {...p}
    className="inline-flex items-center gap-2 rounded-xl px-4 py-2 text-sm font-semibold border border-gray-200 bg-white hover:bg-gray-50"
  />
);

function downloadCSV(filename: string, rows: Record<string, any>[]) {
  if (!rows?.length) return;
  const headers = Object.keys(rows[0]);
  const csv =
    [headers.join(",")]
      .concat(rows.map(r => headers.map(h => JSON.stringify(r[h] ?? "")).join(",")))
      .join("\n");
  const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url; a.download = filename; a.click();
  URL.revokeObjectURL(url);
}

/* ---------------- Component ---------------- */
export default function WaterInputs() {
  const [tab, setTab] = useState<"sources" | "meters" | "costs" | "calibration">("sources");

  // demo data
  const [sources, setSources] = useState<Source[]>([
    { id: "s1", name: "Well A", type: "well", location: "North field", maxFlowLpm: 120, status: "online" },
    { id: "s2", name: "Reservoir 1", type: "reservoir", location: "Farm HQ", capacityM3: 180, maxFlowLpm: 90, status: "maintenance" },
    { id: "s3", name: "Municipal", type: "municipal", location: "Meter at gate", status: "online" },
  ]);

  const [meters, setMeters] = useState<Meter[]>([
    { id: "m1", sourceId: "s1", label: "Well A - Main", serial: "WA-001", unit: "L", kFactor: 1.0, lastReading: 120340, installedAt: "2025-06-01" },
    { id: "m2", sourceId: "s2", label: "Res1 Outlet", serial: "RS-114", unit: "L", kFactor: 0.95, lastReading: 98320, installedAt: "2025-07-10" },
  ]);

  const [tiers, setTiers] = useState<CostTier[]>([
    { id: "t1", from: 0, to: 100, pricePerM3: 2.0 },
    { id: "t2", from: 100, to: 500, pricePerM3: 3.0 },
    { id: "t3", from: 500, to: null, pricePerM3: 4.5 },
  ]);

  /* -------- Tabs header -------- */
  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Water Inputs</h2>
          <p className="text-sm text-gray-600">Manage sources, meters, costs, and calibration.</p>
        </div>
        <div className="flex items-center gap-2">
          <GhostBtn onClick={() => {
            const payload = { sources, meters, tiers };
            const blob = new Blob([JSON.stringify(payload, null, 2)], { type: "application/json" });
            const url = URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url; a.download = "water-inputs.json"; a.click();
            URL.revokeObjectURL(url);
          }}>
            <Upload size={16} />
            Export JSON
          </GhostBtn>
          <label className="inline-flex items-center gap-2 rounded-xl px-4 py-2 text-sm font-semibold border border-gray-200 bg-white hover:bg-gray-50 cursor-pointer">
            Import JSON
            <input
              type="file"
              accept="application/json"
              className="hidden"
              onChange={async (e) => {
                const f = e.target.files?.[0]; if (!f) return;
                const text = await f.text();
                try {
                  const data = JSON.parse(text);
                  if (Array.isArray(data.sources)) setSources(data.sources);
                  if (Array.isArray(data.meters)) setMeters(data.meters);
                  if (Array.isArray(data.tiers)) setTiers(data.tiers);
                  alert("Imported.");
                } catch { alert("Invalid JSON."); }
              }}
            />
          </label>
        </div>
      </div>

      <div className="flex gap-2">
        {(["sources", "meters", "costs", "calibration"] as const).map(key => (
          <button
            key={key}
            onClick={() => setTab(key)}
            className={`px-4 py-2 rounded-xl text-sm font-semibold border ${tab === key ? "text-white" : "text-gray-800 bg-white hover:bg-gray-50"}`}
            style={tab === key ? { background: PRIMARY, borderColor: PRIMARY } : {}}
          >
            {key[0].toUpperCase() + key.slice(1)}
          </button>
        ))}
      </div>

      {tab === "sources" && <SourcesCard sources={sources} setSources={setSources} meters={meters} />}
      {tab === "meters" && <MetersCard meters={meters} setMeters={setMeters} sources={sources} />}
      {tab === "costs" && <CostsCard tiers={tiers} setTiers={setTiers} />}
      {tab === "calibration" && <CalibrationCard meters={meters} setMeters={setMeters} />}
    </div>
  );
}

/* ---------------- Sources ---------------- */
function SourcesCard({ sources, setSources, meters }: {
  sources: Source[];
  setSources: React.Dispatch<React.SetStateAction<Source[]>>;
  meters: Meter[];
}) {
  const [open, setOpen] = useState(false);
  const [edit, setEdit] = useState<Source | null>(null);

  const startAdd = () => { setEdit({ id: crypto.randomUUID(), name: "", type: "well", location: "", capacityM3: undefined, maxFlowLpm: undefined, status: "online" }); setOpen(true); };
  const startEdit = (s: Source) => { setEdit({ ...s }); setOpen(true); };

  const save = () => {
    if (!edit) return;
    if (!edit.name.trim()) { alert("Name required"); return; }
    setSources((arr) => {
      const idx = arr.findIndex(x => x.id === edit.id);
      if (idx === -1) return [...arr, edit];
      const copy = [...arr]; copy[idx] = edit; return copy;
    });
    setOpen(false);
  };

  const remove = (id: string) => {
    const used = meters.some(m => m.sourceId === id);
    if (used) { alert("Source linked to meters. Reassign or delete meters first."); return; }
    setSources(s => s.filter(x => x.id !== id));
  };

  return (
    <Card title="Sources" icon={<Factory size={16} className="text-gray-700" />}>
      <div className="mb-3 flex items-center justify-between">
        <p className="text-sm text-gray-600">Define where water comes from.</p>
        <PrimaryBtn onClick={startAdd}><Plus size={16} /> Add source</PrimaryBtn>
      </div>

      <div className="rounded-xl border border-gray-100 overflow-x-auto">
        <table className="min-w-full text-sm">
          <thead className="bg-gray-50 text-gray-700">
            <tr>
              <th className="px-3 py-2 text-left">Name</th>
              <th className="px-3 py-2 text-left">Type</th>
              <th className="px-3 py-2 text-left">Location</th>
              <th className="px-3 py-2 text-right">Capacity (m³)</th>
              <th className="px-3 py-2 text-right">Max flow (L/min)</th>
              <th className="px-3 py-2 text-left">Status</th>
              <th className="px-3 py-2"></th>
            </tr>
          </thead>
          <tbody>
            {sources.map((s) => (
              <tr key={s.id} className="border-top border-gray-100">
                <td className="px-3 py-2">{s.name}</td>
                <td className="px-3 py-2 capitalize">{s.type}</td>
                <td className="px-3 py-2">{s.location}</td>
                <td className="px-3 py-2 text-right">{s.capacityM3 ?? "—"}</td>
                <td className="px-3 py-2 text-right">{s.maxFlowLpm ?? "—"}</td>
                <td className="px-3 py-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                    s.status === "online" ? "bg-emerald-100 text-emerald-700"
                    : s.status === "maintenance" ? "bg-yellow-100 text-yellow-700"
                    : "bg-red-100 text-red-700"
                  }`}>{s.status}</span>
                </td>
                <td className="px-3 py-2 text-right">
                  <div className="flex items-center justify-end gap-2">
                    <button className="cursor-pointer text-gray-400 hover:text-gray-700" onClick={() => startEdit(s)} title="Edit">
                      <Edit3 size={16} />
                    </button>
                    <button className="cursor-pointer text-gray-400 hover:text-red-600" onClick={() => remove(s.id)} title="Delete">
                      <Trash2 size={16} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
            {sources.length === 0 && (
              <tr><td colSpan={7} className="px-3 py-6 text-center text-gray-500">No sources.</td></tr>
            )}
          </tbody>
        </table>
      </div>

      <div className="mt-4 flex items-center gap-2">
        <GhostBtn onClick={() => downloadCSV("sources.csv", sources as any)}>
          <Download size={16} /> Export CSV
        </GhostBtn>
      </div>

      {open && edit && (
        <Modal title={edit.id ? "Edit source" : "New source"} onClose={() => setOpen(false)}>
          <div className="grid gap-3">
            <div className="grid gap-1.5">
              <label className="text-sm font-medium text-gray-700">Name</label>
              <Input value={edit.name} onChange={(e) => setEdit({ ...edit, name: e.target.value })} />
            </div>
            <div className="grid gap-1.5">
              <label className="text-sm font-medium text-gray-700">Type</label>
              <Select value={edit.type} onChange={(e) => setEdit({ ...edit, type: e.target.value as SourceType })}>
                <option value="well">Well</option>
                <option value="reservoir">Reservoir</option>
                <option value="municipal">Municipal</option>
                <option value="rainwater">Rainwater</option>
              </Select>
            </div>
            <div className="grid gap-1.5">
              <label className="text-sm font-medium text-gray-700">Location</label>
              <Input value={edit.location} onChange={(e) => setEdit({ ...edit, location: e.target.value })} />
            </div>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="text-sm font-medium text-gray-700">Capacity (m³, optional)</label>
                <Input type="number" value={edit.capacityM3 ?? ""} onChange={(e) => setEdit({ ...edit, capacityM3: e.target.value ? Number(e.target.value) : undefined })} />
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Max flow (L/min, optional)</label>
                <Input type="number" value={edit.maxFlowLpm ?? ""} onChange={(e) => setEdit({ ...edit, maxFlowLpm: e.target.value ? Number(e.target.value) : undefined })} />
              </div>
            </div>
            <div className="grid gap-1.5">
              <label className="text-sm font-medium text-gray-700">Status</label>
              <Select value={edit.status} onChange={(e) => setEdit({ ...edit, status: e.target.value as SourceStatus })}>
                <option value="online">Online</option>
                <option value="maintenance">Maintenance</option>
                <option value="offline">Offline</option>
              </Select>
            </div>

            <div className="flex items-center justify-end gap-2 pt-2">
              <GhostBtn onClick={() => setOpen(false)}>Cancel</GhostBtn>
              <PrimaryBtn onClick={save}><Save size={16}/> Save</PrimaryBtn>
            </div>
          </div>
        </Modal>
      )}
    </Card>
  );
}

/* ---------------- Meters ---------------- */
function MetersCard({ meters, setMeters, sources }: {
  meters: Meter[];
  setMeters: React.Dispatch<React.SetStateAction<Meter[]>>;
  sources: Source[];
}) {
  const [open, setOpen] = useState(false);
  const [edit, setEdit] = useState<Meter | null>(null);

  const startAdd = () => {
    setEdit({
      id: crypto.randomUUID(),
      sourceId: sources[0]?.id || "",
      label: "",
      serial: "",
      unit: "L",
      kFactor: 1,
      lastReading: 0,
      installedAt: new Date().toISOString().slice(0, 10),
    });
    setOpen(true);
  };
  const startEdit = (m: Meter) => { setEdit({ ...m }); setOpen(true); };

  const save = () => {
    if (!edit) return;
    if (!edit.label.trim() || !edit.sourceId) { alert("Label and source required"); return; }
    setMeters((arr) => {
      const idx = arr.findIndex(x => x.id === edit.id);
      if (idx === -1) return [...arr, edit];
      const copy = [...arr]; copy[idx] = edit; return copy;
    });
    setOpen(false);
  };

  const remove = (id: string) => setMeters(m => m.filter(x => x.id !== id));

  const addReading = (id: string) => {
    const delta = Number(prompt("Add reading amount (in unit of meter):", "100")) || 0;
    setMeters(m => m.map(x => x.id === id ? { ...x, lastReading: Math.max(0, x.lastReading + delta) } : x));
  };

  const sourceName = (id: string) => sources.find(s => s.id === id)?.name || "—";

  return (
    <Card title="Meters" icon={<Gauge size={16} className="text-gray-700" />}>
      <div className="mb-3 flex items-center justify-between">
        <p className="text-sm text-gray-600">Attach flow meters or counters to sources.</p>
        <PrimaryBtn onClick={startAdd}><Plus size={16} /> Add meter</PrimaryBtn>
      </div>

      <div className="rounded-xl border border-gray-100 overflow-x-auto">
        <table className="min-w-full text-sm">
          <thead className="bg-gray-50 text-gray-700">
            <tr>
              <th className="px-3 py-2 text-left">Label</th>
              <th className="px-3 py-2 text-left">Serial</th>
              <th className="px-3 py-2 text-left">Source</th>
              <th className="px-3 py-2 text-right">k-Factor</th>
              <th className="px-3 py-2 text-right">Reading</th>
              <th className="px-3 py-2 text-left">Unit</th>
              <th className="px-3 py-2 text-left">Installed</th>
              <th className="px-3 py-2"></th>
            </tr>
          </thead>
          <tbody>
            {meters.map((m) => (
              <tr key={m.id} className="border-t border-gray-100">
                <td className="px-3 py-2">{m.label}</td>
                <td className="px-3 py-2">{m.serial || "—"}</td>
                <td className="px-3 py-2">
                  <span className="inline-flex items-center gap-1">
                    <Link2 size={14} className="text-gray-400" />
                    {sourceName(m.sourceId)}
                  </span>
                </td>
                <td className="px-3 py-2 text-right">{m.kFactor.toFixed(3)}</td>
                <td className="px-3 py-2 text-right">{m.lastReading.toLocaleString()}</td>
                <td className="px-3 py-2">{m.unit}</td>
                <td className="px-3 py-2">{m.installedAt}</td>
                <td className="px-3 py-2">
                  <div className="flex items-center justify-end gap-2">
                    <GhostBtn onClick={() => addReading(m.id)}>Add reading</GhostBtn>
                    <button className="cursor-pointer text-gray-400 hover:text-gray-700" onClick={() => startEdit(m)} title="Edit">
                      <Edit3 size={16} />
                    </button>
                    <button className="cursor-pointer text-gray-400 hover:text-red-600" onClick={() => remove(m.id)} title="Delete">
                      <Trash2 size={16} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
            {meters.length === 0 && (
              <tr><td colSpan={8} className="px-3 py-6 text-center text-gray-500">No meters.</td></tr>
            )}
          </tbody>
        </table>
      </div>

      <div className="mt-4 flex items-center gap-2">
        <GhostBtn onClick={() => downloadCSV("meters.csv", meters as any)}>
          <Download size={16} /> Export CSV
        </GhostBtn>
      </div>

      {open && edit && (
        <Modal title={edit.id ? "Edit meter" : "New meter"} onClose={() => setOpen(false)}>
          <div className="grid gap-3">
            <div className="grid gap-1.5">
              <label className="text-sm font-medium text-gray-700">Label</label>
              <Input value={edit.label} onChange={(e) => setEdit({ ...edit, label: e.target.value })} />
            </div>
            <div className="grid gap-1.5">
              <label className="text-sm font-medium text-gray-700">Serial</label>
              <Input value={edit.serial} onChange={(e) => setEdit({ ...edit, serial: e.target.value })} />
            </div>
            <div className="grid gap-1.5">
              <label className="text-sm font-medium text-gray-700">Source</label>
              <Select value={edit.sourceId} onChange={(e) => setEdit({ ...edit, sourceId: e.target.value })}>
                {sources.map(s => <option key={s.id} value={s.id}>{s.name}</option>)}
              </Select>
            </div>
            <div className="grid grid-cols-3 gap-3">
              <div>
                <label className="text-sm font-medium text-gray-700">Unit</label>
                <Select value={edit.unit} onChange={(e) => setEdit({ ...edit, unit: e.target.value as Meter["unit"] })}>
                  <option value="L">Liters</option>
                  <option value="m3">m³</option>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">k-Factor</label>
                <Input type="number" step="0.001" value={edit.kFactor} onChange={(e) => setEdit({ ...edit, kFactor: Math.max(0, Number(e.target.value) || 0) })} />
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Installed at</label>
                <Input type="date" value={edit.installedAt} onChange={(e) => setEdit({ ...edit, installedAt: e.target.value })} />
              </div>
            </div>

            <div className="flex items-center justify-end gap-2 pt-2">
              <GhostBtn onClick={() => setOpen(false)}>Cancel</GhostBtn>
              <PrimaryBtn onClick={save}><Save size={16}/> Save</PrimaryBtn>
            </div>
          </div>
        </Modal>
      )}
    </Card>
  );
}

/* ---------------- Costs ---------------- */
function CostsCard({ tiers, setTiers }: {
  tiers: CostTier[];
  setTiers: React.Dispatch<React.SetStateAction<CostTier[]>>;
}) {
  const [monthly, setMonthly] = useState(120); // m3 estimator

  const sorted = useMemo(() => [...tiers].sort((a, b) => a.from - b.from), [tiers]);

  const addTier = () => setTiers(t => [...t, { id: crypto.randomUUID(), from: (t[t.length - 1]?.to ?? 0), to: null, pricePerM3: 1 }]);
  const removeTier = (id: string) => setTiers(t => t.filter(x => x.id !== id));

  const estimate = useMemo(() => {
    let remaining = Math.max(0, monthly);
    let cost = 0;
    for (const tier of sorted) {
      const to = tier.to ?? Infinity;
      const span = Math.max(0, Math.min(remaining, to - tier.from));
      if (span > 0) {
        cost += span * Math.max(0, tier.pricePerM3);
        remaining -= span;
      }
      if (remaining <= 0) break;
    }
    return { cost, avg: monthly ? cost / monthly : 0 };
  }, [sorted, monthly]);

  return (
    <Card title="Costs & tariffs" icon={<Calculator size={16} className="text-gray-700" />}>
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        <div className="xl:col-span-2">
          <div className="mb-3 flex items-center justify-between">
            <p className="text-sm text-gray-600">Define price per m³ across ranges.</p>
            <PrimaryBtn onClick={addTier}><Plus size={16}/> Add tier</PrimaryBtn>
          </div>

          <div className="rounded-xl border border-gray-100 overflow-x-auto">
            <table className="min-w-full text-sm">
              <thead className="bg-gray-50 text-gray-700">
                <tr>
                  <th className="px-3 py-2 text-right">From (m³)</th>
                  <th className="px-3 py-2 text-right">To (m³)</th>
                  <th className="px-3 py-2 text-right">Price / m³</th>
                  <th className="px-3 py-2"></th>
                </tr>
              </thead>
              <tbody>
                {sorted.map((t) => (
                  <tr key={t.id} className="border-t border-gray-100">
                    <td className="px-3 py-2 text-right">
                      <Input type="number" value={t.from}
                             onChange={(e) => setTiers(arr => arr.map(x => x.id === t.id ? { ...x, from: Math.max(0, Number(e.target.value) || 0) } : x))} />
                    </td>
                    <td className="px-3 py-2 text-right">
                      <Input type="number" value={t.to ?? ""}
                             onChange={(e) => setTiers(arr => arr.map(x => x.id === t.id ? { ...x, to: e.target.value ? Math.max(0, Number(e.target.value) || 0) : null } : x))} />
                    </td>
                    <td className="px-3 py-2 text-right">
                      <Input type="number" step="0.01" value={t.pricePerM3}
                             onChange={(e) => setTiers(arr => arr.map(x => x.id === t.id ? { ...x, pricePerM3: Math.max(0, Number(e.target.value) || 0) } : x))} />
                    </td>
                    <td className="px-3 py-2 text-right">
                      <button className="cursor-pointer text-gray-400 hover:text-red-600" onClick={() => removeTier(t.id)} title="Delete">
                        <Trash2 size={16}/>
                      </button>
                    </td>
                  </tr>
                ))}
                {sorted.length === 0 && (
                  <tr><td colSpan={4} className="px-3 py-6 text-center text-gray-500">No tiers.</td></tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        <div>
          <div className="rounded-2xl border border-gray-100 p-4">
            <p className="text-sm font-semibold text-gray-900 mb-2">Monthly cost estimator</p>
            <div className="grid gap-2">
              <label className="text-sm text-gray-700">Usage (m³)</label>
              <Input type="number" value={monthly} onChange={(e) => setMonthly(Math.max(0, Number(e.target.value) || 0))} />
            </div>
            <div className="mt-4 grid grid-cols-2 gap-3">
              <div className="rounded-xl border border-gray-200 p-3">
                <p className="text-xs text-gray-500">Estimated cost</p>
                <p className="text-xl font-bold text-gray-900">{estimate.cost.toFixed(2)}</p>
              </div>
              <div className="rounded-xl border border-gray-200 p-3">
                <p className="text-xs text-gray-500">Avg price / m³</p>
                <p className="text-xl font-bold text-gray-900">{estimate.avg.toFixed(2)}</p>
              </div>
            </div>
            <div className="mt-4">
              <GhostBtn onClick={() => downloadCSV("tiers.csv", tiers as any)}>
                <Download size={16}/> Export tiers CSV
              </GhostBtn>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}

/* ---------------- Calibration ---------------- */
function CalibrationCard({ meters, setMeters }: {
  meters: Meter[];
  setMeters: React.Dispatch<React.SetStateAction<Meter[]>>;
}) {
  const [meterId, setMeterId] = useState<string>(meters[0]?.id || "");
  const [measuredVolume, setMeasuredVolume] = useState<number>(100); // in chosen unit below
  const [unit, setUnit] = useState<"L" | "m3">("L");
  const [pulses, setPulses] = useState<number>(100);

  const selected = meters.find(m => m.id === meterId) || null;

  const kFactor = useMemo(() => {
    // pulses per unit
    const volInUnit = unit === "L" ? measuredVolume : measuredVolume * 1000;
    if (volInUnit <= 0) return 0;
    return pulses / volInUnit;
  }, [measuredVolume, pulses, unit]);

  const apply = () => {
    if (!selected) { alert("Choose a meter"); return; }
    setMeters(arr => arr.map(m => m.id === selected.id ? { ...m, kFactor: kFactor } : m));
    alert("Calibration saved.");
  };

  return (
    <Card title="Calibration" icon={<Wrench size={16} className="text-gray-700" />}>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="grid gap-2">
          <label className="text-sm font-medium text-gray-700">Meter</label>
          <Select value={meterId} onChange={(e) => setMeterId(e.target.value)}>
            {meters.map(m => <option key={m.id} value={m.id}>{m.label} ({m.serial || "no-serial"})</option>)}
          </Select>
        </div>

        <div className="grid gap-2">
          <label className="text-sm font-medium text-gray-700">Measured volume</label>
          <div className="grid grid-cols-3 gap-2">
            <Input type="number" value={measuredVolume} onChange={(e) => setMeasuredVolume(Math.max(0, Number(e.target.value) || 0))} />
            <Select value={unit} onChange={(e) => setUnit(e.target.value as "L" | "m3")}>
              <option value="L">L</option>
              <option value="m3">m³</option>
            </Select>
            <div className="rounded-xl border border-gray-200 px-3 py-2 text-sm text-gray-700 flex items-center">
              = {unit === "L" ? measuredVolume : (measuredVolume * 1000)} L
            </div>
          </div>
        </div>

        <div className="grid gap-2">
          <label className="text-sm font-medium text-gray-700">Pulses counted</label>
          <Input type="number" value={pulses} onChange={(e) => setPulses(Math.max(0, Math.floor(Number(e.target.value) || 0)))} />
        </div>
      </div>

      <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="rounded-xl border border-gray-200 p-3">
          <p className="text-xs text-gray-500">k-Factor (pulses per liter)</p>
          <p className="text-xl font-bold text-gray-900">{kFactor.toFixed(4)}</p>
        </div>
        <div className="rounded-xl border border-gray-200 p-3">
          <p className="text-xs text-gray-500">Current meter k-Factor</p>
          <p className="text-xl font-bold text-gray-900">{selected ? selected.kFactor.toFixed(4) : "—"}</p>
        </div>
        <div className="flex items-end">
          <PrimaryBtn onClick={apply}><Save size={16}/> Apply to meter</PrimaryBtn>
        </div>
      </div>
    </Card>
  );
}

/* ---------------- Modal ---------------- */
function Modal({ title, children, onClose }: { title: string; children: React.ReactNode; onClose: () => void }) {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />
      <div className="relative w-full max-w-lg bg-white rounded-2xl shadow-2xl">
        <div className="px-5 py-4 border-b border-gray-100 flex items-center justify-between">
          <h3 className="text-sm font-semibold text-gray-900">{title}</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-700">✕</button>
        </div>
        <div className="p-5">{children}</div>
      </div>
    </div>
  );
}
