import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "../globals.css";
import AppShell from "@/components/layout/AppShell";
import StoreProvider from "@/components/StoreProvider";
import AuthWrapper from "@/components/AuthWrapper";
import ClientHydrator from "@/components/auth/ClientHydrator";

const geistSans = Geist({ variable: "--font-geist-sans", subsets: ["latin"] });
const geistMono = Geist_Mono({ variable: "--font-geist-mono", subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Farmti",
  description: "Smart Farm Management",
};

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  return (
    <StoreProvider>
      <ClientHydrator />
      <AuthWrapper>
        <AppShell>
          <div className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
            {children}
          </div>
        </AppShell>
      </AuthWrapper>
    </StoreProvider>
  );
}
