"use client";
import React, { useState, useEffect } from "react";
import {
  AlertTriangle,
  Droplets,
  Thermometer,
  Activity,
  Zap,
  CheckCircle,
  Clock,
  AlertCircle,
  ChevronRight,
  TrendingUp,
  TrendingDown,
  MapPin,
  Wifi,
  WifiOff,
  Battery,
  Sun,
  Cloud,
  Eye,
  MoreVertical,
  Play,
  Pause,
  BarChart3,
} from "lucide-react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
} from "recharts";

// Animated Counter Component
const AnimatedCounter: React.FC<{ 
  value: string; 
  duration?: number; 
  prefix?: string;
  suffix?: string;
}> = ({ value, duration = 2000, prefix = "", suffix = "" }) => {
  const [count, setCount] = useState(0);
  const numericValue = parseFloat(value.replace(/[^\d.]/g, ''));
  
  useEffect(() => {
    let startTime: number;
    let animationId: number;
    
    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = (timestamp - startTime) / duration;
      
      if (progress < 1) {
        setCount(numericValue * progress);
        animationId = requestAnimationFrame(animate);
      } else {
        setCount(numericValue);
      }
    };
    
    animationId = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(animationId);
  }, [numericValue, duration]);
  
  return (
    <span>
      {prefix}{count.toFixed(value.includes('.') ? 1 : 0)}{suffix}
    </span>
  );
};

// Enhanced Stat Card with Animation
const EnhancedStatCard: React.FC<{
  title: string;
  value: string;
  change: string;
  trend: 'up' | 'down';
  icon: React.ReactNode;
  color: string;
  percentage: number;
  delay?: number;
}> = ({ title, value, change, trend, icon, color, percentage, delay = 0 }) => {
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), delay);
    return () => clearTimeout(timer);
  }, [delay]);

  const gradientColors = {
    'bg-[#21c45d]': 'from-[#21c45d] to-[#1aa052]',
    'bg-green-500': 'from-green-500 to-emerald-500',
    'bg-emerald-500': 'from-emerald-500 to-teal-500',
    'bg-blue-500': 'from-blue-500 to-indigo-500',
  };

  const bgGradient = gradientColors[color as keyof typeof gradientColors] || 'from-gray-500 to-gray-600';

  return (
    <div className={`group relative bg-white rounded-3xl p-6 shadow-lg border border-gray-100 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500 overflow-hidden ${
      isVisible ? 'animate-in slide-in-from-bottom-4 fade-in' : 'opacity-0'
    }`}>
      {/* Background Pattern */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-gray-50 to-transparent rounded-full -translate-y-16 translate-x-16 opacity-50"></div>
      
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className={`p-3 rounded-2xl bg-gradient-to-br ${bgGradient} shadow-lg group-hover:scale-110 transition-transform duration-300`}>
          {icon}
        </div>
        <div className="flex items-center space-x-1">
          {trend === 'up' ? (
            <TrendingUp className="w-4 h-4 text-green-500" />
          ) : (
            <TrendingDown className="w-4 h-4 text-red-500" />
          )}
          <span className={`text-sm font-bold ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
            {change}
          </span>
        </div>
      </div>

      {/* Value */}
      <div className="mb-4">
        <h3 className="text-3xl font-bold text-gray-900 mb-1">
          <AnimatedCounter 
            value={value} 
            suffix={value.includes('L') ? 'L' : value.includes('%') ? '%' : ''}
          />
        </h3>
        <p className="text-gray-600 font-medium">{title}</p>
      </div>

      {/* Progress Ring */}
      <div className="relative w-16 h-16">
        <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 64 64">
          <circle
            cx="32"
            cy="32"
            r="28"
            fill="none"
            stroke="#f3f4f6"
            strokeWidth="4"
          />
          <circle
            cx="32"
            cy="32"
            r="28"
            fill="none"
            stroke="url(#gradient)"
            strokeWidth="4"
            strokeLinecap="round"
            strokeDasharray={`${2 * Math.PI * 28}`}
            strokeDashoffset={`${2 * Math.PI * 28 * (1 - (isVisible ? percentage / 100 : 0))}`}
            className="transition-all duration-2000 ease-out"
          />
          <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#21c45d" />
              <stop offset="100%" stopColor="#1aa052" />
            </linearGradient>
          </defs>
        </svg>
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-sm font-bold text-gray-700">
            <AnimatedCounter value={percentage.toString()} suffix="%" />
          </span>
        </div>
      </div>
    </div>
  );
};

// Weather Widget
const WeatherWidget: React.FC = () => {
  return (
    <div className="bg-gradient-to-br from-blue-500 via-blue-600 to-purple-600 rounded-3xl p-6 text-white shadow-xl hover:shadow-2xl transition-all duration-300 group">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
            <Sun className="w-6 h-6" />
          </div>
          <div>
            <p className="text-white/80 text-sm">Today's Weather</p>
            <p className="text-2xl font-bold">28°C</p>
          </div>
        </div>
        <div className="text-right">
          <p className="text-white/80 text-sm">Humidity</p>
          <p className="text-xl font-bold">65%</p>
        </div>
      </div>
      
      <div className="flex items-center space-x-4 text-sm text-white/80">
        <div className="flex items-center space-x-1">
          <Droplets className="w-4 h-4" />
          <span>Rain: 0%</span>
        </div>
        <div className="flex items-center space-x-1">
          <Activity className="w-4 h-4" />
          <span>Wind: 12 km/h</span>
        </div>
      </div>
    </div>
  );
};

// Enhanced Action Item
const EnhancedActionItem: React.FC<{ 
  item: any; 
  index: number;
}> = ({ item, index }) => {
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), index * 100);
    return () => clearTimeout(timer);
  }, [index]);

  const statusConfig = {
    pending: { 
      color: "bg-gradient-to-r from-yellow-400 to-orange-400", 
      textColor: "text-yellow-800",
      bgColor: "bg-yellow-50",
      icon: Clock 
    },
    completed: { 
      color: "bg-gradient-to-r from-green-400 to-emerald-500", 
      textColor: "text-green-800",
      bgColor: "bg-green-50",
      icon: CheckCircle 
    },
    urgent: { 
      color: "bg-gradient-to-r from-red-400 to-pink-500", 
      textColor: "text-red-800",
      bgColor: "bg-red-50",
      icon: AlertCircle 
    },
  };

  const config = statusConfig[item.status as keyof typeof statusConfig];
  const StatusIcon = config.icon;

  return (
    <div className={`${config.bgColor} rounded-2xl p-4 border border-white/50 hover:shadow-lg transition-all duration-300 group cursor-pointer ${
      isVisible ? 'animate-in slide-in-from-right-2 fade-in' : 'opacity-0'
    }`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className={`p-3 rounded-xl ${config.color} shadow-md group-hover:scale-110 transition-transform duration-200`}>
            <StatusIcon size={18} className="text-white" />
          </div>
          <div>
            <h4 className="font-bold text-gray-900 group-hover:text-[#21c45d] transition-colors">
              {item.title}
            </h4>
            <p className="text-gray-600 text-sm">{item.description}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className="text-xs font-medium text-gray-500">{item.time}</span>
          <ChevronRight size={16} className="text-gray-400 group-hover:text-[#21c45d] group-hover:translate-x-1 transition-all" />
        </div>
      </div>
    </div>
  );
};

// Enhanced Zone Card
const EnhancedZoneCard: React.FC<{ 
  zone: any; 
  index: number;
}> = ({ zone, index }) => {
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), index * 150);
    return () => clearTimeout(timer);
  }, [index]);

  const statusColors = {
    good: "from-green-400 to-emerald-500",
    warning: "from-yellow-400 to-orange-500",
    normal: "from-blue-400 to-indigo-500",
  };

  return (
    <div className={`bg-white rounded-3xl p-6 shadow-lg border border-gray-100 hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 group ${
      isVisible ? 'animate-in slide-in-from-bottom-4 fade-in' : 'opacity-0'
    }`}>
      {/* Header */}
      <div className="flex items-start justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className={`w-12 h-12 rounded-2xl bg-gradient-to-br ${statusColors[zone.status as keyof typeof statusColors]} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
            <MapPin className="w-6 h-6 text-white" />
          </div>
          <div>
            <h4 className="font-bold text-gray-900 text-lg group-hover:text-[#21c45d] transition-colors">{zone.name}</h4>
            <p className="text-gray-600 flex items-center space-x-1">
              <span>{zone.crop}</span>
              <span className="text-gray-400">•</span>
              <span>{zone.area}</span>
            </p>
          </div>
        </div>
        <button className="p-2 hover:bg-gray-100 rounded-xl transition-colors">
          <MoreVertical className="w-4 h-4 text-gray-400" />
        </button>
      </div>

      {/* Moisture Progress */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-700">Soil Moisture</span>
          <span className="text-xl font-bold text-gray-900">{zone.moisture}%</span>
        </div>
        <div className="w-full h-3 bg-gray-200 rounded-full overflow-hidden">
          <div
            className={`h-full bg-gradient-to-r ${statusColors[zone.status as keyof typeof statusColors]} rounded-full transition-all duration-1000 ease-out`}
            style={{ width: isVisible ? `${zone.moisture}%` : '0%' }}
          />
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center p-3 bg-gray-50 rounded-xl group-hover:bg-[#21c45d]/5 transition-colors">
          <p className="text-2xl font-bold text-[#21c45d]">{zone.usage}</p>
          <p className="text-xs text-gray-600">Usage Today</p>
        </div>
        <div className="text-center p-3 bg-gray-50 rounded-xl group-hover:bg-[#21c45d]/5 transition-colors">
          <p className="text-2xl font-bold text-[#21c45d]">
            {zone.status === 'good' ? '98%' : zone.status === 'warning' ? '75%' : '88%'}
          </p>
          <p className="text-xs text-gray-600">Efficiency</p>
        </div>
      </div>

      {/* Action Button */}
      <button className="w-full bg-gradient-to-r from-[#21c45d] to-[#1aa052] text-white py-3 px-4 rounded-xl font-medium hover:shadow-lg hover:scale-105 transition-all duration-200 flex items-center justify-center space-x-2">
        <Eye className="w-4 h-4" />
        <span>View Details</span>
      </button>
    </div>
  );
};

// Main Dashboard Component
const Dashboard: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [activeChart, setActiveChart] = useState<'line' | 'area'>('area');

  const statsData = [
    {
      title: "Total Water Usage",
      value: "40,622L",
      change: "+12.5%",
      trend: "up" as const,
      icon: <Droplets className="w-6 h-6 text-white" />,
      color: "bg-[#21c45d]",
      percentage: 78,
      delay: 0,
    },
    {
      title: "Avg Soil Moisture",
      value: "76.0%",
      change: "+2.4%",
      trend: "up" as const,
      icon: <Thermometer className="w-6 h-6 text-white" />,
      color: "bg-green-500",
      percentage: 76,
      delay: 200,
    },
    {
      title: "System Health",
      value: "97.2%",
      change: "Optimal",
      trend: "up" as const,
      icon: <Activity className="w-6 h-6 text-white" />,
      color: "bg-emerald-500",
      percentage: 97,
      delay: 400,
    },
    {
      title: "Energy Efficiency",
      value: "89.5%",
      change: "+5.2%",
      trend: "up" as const,
      icon: <Zap className="w-6 h-6 text-white" />,
      color: "bg-blue-500",
      percentage: 89,
      delay: 600,
    },
  ];

  const waterUsageData = [
    { day: "Mon", usage: 1200, target: 1100, efficiency: 92 },
    { day: "Tue", usage: 1350, target: 1200, efficiency: 89 },
    { day: "Wed", usage: 980, target: 1150, efficiency: 95 },
    { day: "Thu", usage: 1100, target: 1000, efficiency: 91 },
    { day: "Fri", usage: 1280, target: 1200, efficiency: 88 },
    { day: "Sat", usage: 1320, target: 1250, efficiency: 86 },
    { day: "Sun", usage: 1400, target: 1300, efficiency: 85 },
  ];

  const zoneData = [
    { name: "North Field", crop: "Tomatoes", area: "3.2ha", moisture: 84, usage: "544L", status: "good" },
    { name: "South Field", crop: "Peppers", area: "2.8ha", moisture: 76, usage: "395L", status: "warning" },
    { name: "East Block", crop: "Cucumbers", area: "4.1ha", moisture: 92, usage: "672L", status: "good" },
    { name: "West Garden", crop: "Herbs", area: "1.5ha", moisture: 88, usage: "234L", status: "normal" },
  ];

  const actionItems = [
    { id: "1", title: "North Field", description: "Irrigation scheduled", status: "pending", time: "10:30 AM" },
    { id: "2", title: "South Field", description: "Fertilizer application", status: "completed", time: "08:15 AM" },
    { id: "3", title: "East Block", description: "Low moisture detected", status: "urgent", time: "Now" },
  ];

  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-[#21c45d]/20 border-t-[#21c45d] rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 font-medium">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8 p-6 bg-gradient-to-br from-gray-50 to-white min-h-screen">
      {/* Welcome Section */}
      {/* <div className="text-center mb-12 animate-in fade-in slide-in-from-top-4 duration-1000">
        <h1 className="text-4xl font-bold text-gray-900 mb-2">
          Welcome to Your Smart Farm
        </h1>
        <p className="text-xl text-gray-600">
          Real-time insights and intelligent automation at your fingertips
        </p>
      </div> */}

      {/* Alert Banner */}
      {/* <div className="bg-gradient-to-r from-orange-100 via-red-50 to-pink-100 border border-orange-200 rounded-3xl p-6 shadow-lg animate-in slide-in-from-top-2 duration-700">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-gradient-to-r from-orange-400 to-red-500 rounded-2xl shadow-lg animate-pulse">
            <AlertTriangle className="w-6 h-6 text-white" />
          </div>
          <div className="flex-1">
            <h3 className="font-bold text-gray-900 mb-1">2 Active Alerts</h3>
            <p className="text-gray-700">South Field moisture dropped below threshold. East Block requires attention.</p>
          </div>
          <button className="bg-gradient-to-r from-[#21c45d] to-[#1aa052] text-white px-6 py-3 rounded-xl font-medium hover:shadow-lg hover:scale-105 transition-all duration-200">
            View Details
          </button>
        </div>
      </div> */}

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
        {statsData.map((stat, index) => (
          <EnhancedStatCard key={index} {...stat} />
        ))}
      </div>

      {/* Weather + Quick Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-1">
          <WeatherWidget />
        </div>
        <div className="lg:col-span-3 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-3xl p-6 shadow-lg border border-gray-100 text-center hover:shadow-xl transition-shadow">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <BarChart3 className="w-6 h-6 text-white" />
            </div>
            <p className="text-2xl font-bold text-gray-900">15</p>
            <p className="text-gray-600 text-sm">Active Zones</p>
          </div>
          <div className="bg-white rounded-3xl p-6 shadow-lg border border-gray-100 text-center hover:shadow-xl transition-shadow">
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Wifi className="w-6 h-6 text-white" />
            </div>
            <p className="text-2xl font-bold text-gray-900">142</p>
            <p className="text-gray-600 text-sm">Sensors Online</p>
          </div>
          <div className="bg-white rounded-3xl p-6 shadow-lg border border-gray-100 text-center hover:shadow-xl transition-shadow">
            <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Battery className="w-6 h-6 text-white" />
            </div>
            <p className="text-2xl font-bold text-gray-900">94%</p>
            <p className="text-gray-600 text-sm">Battery Level</p>
          </div>
        </div>
      </div>

      {/* Chart Section */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
        <div className="xl:col-span-2 bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h3 className="text-2xl font-bold text-gray-900">Water Usage Analytics</h3>
              <p className="text-gray-600">Daily consumption trends and efficiency metrics</p>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex bg-gray-100 rounded-xl p-1">
                <button
                  onClick={() => setActiveChart('area')}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                    activeChart === 'area' 
                      ? 'bg-[#21c45d] text-white shadow-md' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Area
                </button>
                <button
                  onClick={() => setActiveChart('line')}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                    activeChart === 'line' 
                      ? 'bg-[#21c45d] text-white shadow-md' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Line
                </button>
              </div>
            </div>
          </div>

          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              {activeChart === 'area' ? (
                <AreaChart data={waterUsageData}>
                  <defs>
                    <linearGradient id="colorUsage" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#21c45d" stopOpacity={0.3}/>
                      <stop offset="95%" stopColor="#21c45d" stopOpacity={0}/>
                    </linearGradient>
                    <linearGradient id="colorTarget" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#10b981" stopOpacity={0.3}/>
                      <stop offset="95%" stopColor="#10b981" stopOpacity={0}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis dataKey="day" stroke="#6b7280" fontSize={12} />
                  <YAxis stroke="#6b7280" fontSize={12} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: "white",
                      border: "none",
                      borderRadius: "16px",
                      boxShadow: "0 20px 25px -5px rgb(0 0 0 / 0.1)",
                    }}
                  />
                  <Area
                    type="monotone"
                    dataKey="usage"
                    stroke="#21c45d"
                    fillOpacity={1}
                    fill="url(#colorUsage)"
                    strokeWidth={3}
                  />
                  <Area
                    type="monotone"
                    dataKey="target"
                    stroke="#10b981"
                    fillOpacity={1}
                    fill="url(#colorTarget)"
                    strokeWidth={2}
                    strokeDasharray="5 5"
                  />
                </AreaChart>
              ) : (
                <LineChart data={waterUsageData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis dataKey="day" stroke="#6b7280" fontSize={12} />
                  <YAxis stroke="#6b7280" fontSize={12} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: "white",
                      border: "none",
                      borderRadius: "16px",
                      boxShadow: "0 20px 25px -5px rgb(0 0 0 / 0.1)",
                    }}
                  />
                  <Line
                    type="monotone"
                    dataKey="usage"
                    stroke="#21c45d"
                    strokeWidth={3}
                    dot={{ fill: "#21c45d", strokeWidth: 2, r: 6 }}
                    activeDot={{ r: 8, fill: "#176c45", stroke: "#fff", strokeWidth: 2 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="target"
                    stroke="#10b981"
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    dot={{ fill: "#10b981", strokeWidth: 2, r: 4 }}
                  />
                </LineChart>
              )}
            </ResponsiveContainer>
          </div>
        </div>

        <div className="space-y-6">
          {/* Action Items */}
          <div className="bg-white rounded-3xl p-6 shadow-lg border border-gray-100">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900">Action Items</h3>
              <span className="bg-red-100 text-red-600 text-xs font-bold px-2 py-1 rounded-full">
                {actionItems.filter(item => item.status === 'urgent').length} Urgent
              </span>
            </div>
            <div className="space-y-3">
              {actionItems.map((item, index) => (
                <EnhancedActionItem key={item.id} item={item} index={index} />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Zones Grid */}
      <div>
        <div className="flex items-center justify-between mb-8">
          <div>
            <h3 className="text-2xl font-bold text-gray-900">Farm Zones</h3>
            <p className="text-gray-600">Real-time monitoring of all agricultural zones</p>
          </div>
          <button className="bg-gradient-to-r from-[#21c45d] to-[#1aa052] text-white px-6 py-3 rounded-xl font-medium hover:shadow-lg hover:scale-105 transition-all duration-200 flex items-center space-x-2">
            <MapPin className="w-4 h-4" />
            <span>View Map</span>
          </button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
          {zoneData.map((zone, index) => (
            <EnhancedZoneCard key={zone.name} zone={zone} index={index} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
