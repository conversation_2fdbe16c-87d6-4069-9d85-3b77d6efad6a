"use client"
import React, { useMemo, useState } from "react";
import {
  Save,
  Leaf,
  Map,
  Layers,
  Ruler,
  Bell,
  Droplets,
  Thermometer,
  Wrench,
  Link2,
  Plus,
  Trash2,
  RefreshCw,
} from "lucide-react";

type UnitSystem = "metric" | "imperial";
type MoistureBand = { min: number; max: number };

interface SensorRow {
  id: string;
  name: string;
  type: "soil-moisture" | "temperature" | "flow" | "pressure";
  plot: string;
  deviceId: string;
  status: "online" | "offline" | "unknown";
}

const PRIMARY = "#21c45d";

const SectionCard: React.FC<{ title: string; icon?: React.ReactNode; children: React.ReactNode }> = ({
  title,
  icon,
  children,
}) => (
  <div className="bg-white rounded-2xl border border-gray-100 shadow-sm">
    <div className="px-5 py-4 border-b border-gray-100 flex items-center gap-2">
      {icon}
      <h3 className="text-sm font-semibold text-gray-900">{title}</h3>
    </div>
    <div className="p-5">{children}</div>
  </div>
);

const Label: React.FC<{ htmlFor?: string; children: React.ReactNode }> = ({ htmlFor, children }) => (
  <label htmlFor={htmlFor} className="text-sm font-medium text-gray-700">
    {children}
  </label>
);

const Input: React.FC<React.InputHTMLAttributes<HTMLInputElement>> = (props) => (
  <input
    {...props}
    className={
      "w-full rounded-xl border border-gray-200 bg-white px-3 py-2 text-sm text-gray-900 outline-none " +
      "focus:ring-2 focus:ring-offset-0 " +
      `focus:ring-[${PRIMARY}]`
    }
  />
);

const Select: React.FC<React.SelectHTMLAttributes<HTMLSelectElement>> = (props) => (
  <select
    {...props}
    className={
      "w-full rounded-xl border border-gray-200 bg-white px-3 py-2 text-sm text-gray-900 outline-none " +
      "focus:ring-2 focus:ring-offset-0 " +
      `focus:ring-[${PRIMARY}]`
    }
  />
);

const Toggle: React.FC<{ checked: boolean; onChange: (v: boolean) => void; id?: string }> = ({
  checked,
  onChange,
  id,
}) => (
  <button
    id={id}
    type="button"
    onClick={() => onChange(!checked)}
    className={`h-6 w-11 rounded-full transition-colors ${
      checked ? "" : "bg-gray-200"
    }`}
    style={{ backgroundColor: checked ? PRIMARY : undefined }}
    aria-pressed={checked}
  >
    <span
      className={`block h-5 w-5 rounded-full bg-white shadow transform transition-transform ${
        checked ? "translate-x-6" : "translate-x-1"
      }`}
    />
  </button>
);

const Pill: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <span className="inline-flex items-center rounded-full border border-gray-200 px-2.5 py-1 text-xs text-gray-700">
    {children}
  </span>
);

const ActionButton: React.FC<
  { children: React.ReactNode } & React.ButtonHTMLAttributes<HTMLButtonElement>
> = ({ children, ...rest }) => (
  <button
    {...rest}
    className={`inline-flex items-center gap-2 rounded-xl px-4 py-2 text-sm font-semibold text-white shadow-sm`}
    style={{ background: PRIMARY }}
  >
    {children}
  </button>
);

const GhostButton: React.FC<
  { children: React.ReactNode } & React.ButtonHTMLAttributes<HTMLButtonElement>
> = ({ children, ...rest }) => (
  <button
    {...rest}
    className={`inline-flex items-center gap-2 rounded-xl px-4 py-2 text-sm font-semibold border border-gray-200 text-gray-800 bg-white hover:bg-gray-50`}
  >
    {children}
  </button>
);

const Configuration: React.FC = () => {
  // Farm profile
  const [farmName, setFarmName] = useState("Farmti Demo Farm");
  const [location, setLocation] = useState("Settat, Morocco");
  const [unitSystem, setUnitSystem] = useState<UnitSystem>("metric");

  // Plots
  const [plots, setPlots] = useState<string[]>(["North-01", "North-02", "South-01"]);
  const [newPlot, setNewPlot] = useState("");

  // Sensors
  const [sensors, setSensors] = useState<SensorRow[]>([
    {
      id: "S-001",
      name: "Soil M 1",
      type: "soil-moisture",
      plot: "North-01",
      deviceId: "ESP32-7C2A",
      status: "online",
    },
    {
      id: "S-014",
      name: "Temp 3",
      type: "temperature",
      plot: "South-01",
      deviceId: "ESP32-19F0",
      status: "offline",
    },
  ]);
  const [pairCode, setPairCode] = useState("");

  // Thresholds
  const [moisture, setMoisture] = useState<MoistureBand>({ min: 22, max: 28 });
  const [tempWarn, setTempWarn] = useState<number>(38);

  // Water system
  const [pumpCapacity, setPumpCapacity] = useState<number>(2.5); // m³/h
  const [maxConcurrentValves, setMaxConcurrentValves] = useState<number>(3);

  // Map defaults
  const [basemap, setBasemap] = useState<"satellite" | "streets" | "terrain">("satellite");
  const [plotsOpacity, setPlotsOpacity] = useState<number>(70);

  // Notifications
  const [notifyLeaks, setNotifyLeaks] = useState(true);
  const [notifyOffline, setNotifyOffline] = useState(true);
  const [notifyThreshold, setNotifyThreshold] = useState(true);
  const [email, setEmail] = useState("<EMAIL>");

  // API
  const [webhookUrl, setWebhookUrl] = useState("");
  const [apiKey, setApiKey] = useState<string | null>(null);

  const unitHints = useMemo(
    () => ({
      moisture: "%",
      temp: unitSystem === "metric" ? "°C" : "°F",
      flow: unitSystem === "metric" ? "m³/h" : "gpm",
      length: unitSystem === "metric" ? "m" : "ft",
      area: unitSystem === "metric" ? "ha" : "ac",
    }),
    [unitSystem]
  );

  const addPlot = () => {
    const p = newPlot.trim();
    if (!p) return;
    if (plots.includes(p)) return;
    setPlots((s) => [...s, p]);
    setNewPlot("");
  };

  const removePlot = (p: string) => setPlots((s) => s.filter((x) => x !== p));

  const addSensor = () => {
    const code = pairCode.trim();
    if (!code) return;
    const newRow: SensorRow = {
      id: `S-${Math.floor(100 + Math.random() * 900)}`,
      name: `Sensor ${sensors.length + 1}`,
      type: "soil-moisture",
      plot: plots[0] ?? "Plot-1",
      deviceId: code,
      status: "unknown",
    };
    setSensors((s) => [newRow, ...s]);
    setPairCode("");
  };

  const removeSensor = (id: string) => setSensors((s) => s.filter((x) => x.id !== id));

  const generateApiKey = () => {
    const k = crypto?.randomUUID?.() ?? Math.random().toString(36).slice(2);
    setApiKey(`farmti_${k.replace(/-/g, "")}`);
  };

  const convertIfImperial = (c: number) => (unitSystem === "metric" ? c : Math.round((c * 9) / 5 + 32));

  const handleSaveAll = () => {
    // Wire to your API later
    const payload = {
      farm: { farmName, location, unitSystem },
      plots,
      sensors,
      thresholds: {
        moisture,
        tempWarn: unitSystem === "metric" ? tempWarn : convertIfImperial(tempWarn),
      },
      water: { pumpCapacity, maxConcurrentValves },
      map: { basemap, plotsOpacity },
      notifications: { notifyLeaks, notifyOffline, notifyThreshold, email },
      integrations: { webhookUrl, apiKey },
    };
    console.info("CONFIG_SAVE", payload);
    alert("Configuration saved.");
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Configuration</h2>
          <p className="text-sm text-gray-600">Set farm profile, plots, sensors, thresholds, and defaults.</p>
        </div>
        <div className="flex items-center gap-3">
          <GhostButton type="button" onClick={() => window.location.reload()}>
            <RefreshCw size={16} />
            Reset page
          </GhostButton>
          <ActionButton type="button" onClick={handleSaveAll}>
            <Save size={16} />
            Save changes
          </ActionButton>
        </div>
      </div>

      {/* Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        {/* Col 1 */}
        <div className="space-y-6">
          <SectionCard title="Farm profile" icon={<Leaf size={16} className="text-gray-700" />}>
            <div className="grid grid-cols-1 gap-4">
              <div className="grid gap-1.5">
                <Label htmlFor="farmName">Farm name</Label>
                <Input id="farmName" value={farmName} onChange={(e) => setFarmName(e.target.value)} />
              </div>
              <div className="grid gap-1.5">
                <Label htmlFor="location">Location</Label>
                <Input id="location" value={location} onChange={(e) => setLocation(e.target.value)} />
              </div>
              <div className="grid gap-1.5">
                <Label htmlFor="units">Units</Label>
                <Select
                  id="units"
                  value={unitSystem}
                  onChange={(e) => setUnitSystem(e.target.value as UnitSystem)}
                >
                  <option value="metric">Metric (°C, m, m², m³/h)</option>
                  <option value="imperial">Imperial (°F, ft, acre, gpm)</option>
                </Select>
              </div>
            </div>
          </SectionCard>

          <SectionCard title="Thresholds" icon={<Thermometer size={16} className="text-gray-700" />}>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-1.5">
                <Label>Soil moisture min ({unitHints.moisture})</Label>
                <Input
                  type="number"
                  value={moisture.min}
                  onChange={(e) => setMoisture((m) => ({ ...m, min: Number(e.target.value) }))}
                />
              </div>
              <div className="grid gap-1.5">
                <Label>Soil moisture max ({unitHints.moisture})</Label>
                <Input
                  type="number"
                  value={moisture.max}
                  onChange={(e) => setMoisture((m) => ({ ...m, max: Number(e.target.value) }))}
                />
              </div>
              <div className="grid gap-1.5 col-span-2">
                <Label>Temperature warning ({unitHints.temp})</Label>
                <Input
                  type="number"
                  value={tempWarn}
                  onChange={(e) => setTempWarn(Number(e.target.value))}
                />
              </div>
            </div>
          </SectionCard>

          <SectionCard title="Water system" icon={<Droplets size={16} className="text-gray-700" />}>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-1.5">
                <Label>Pump capacity ({unitHints.flow})</Label>
                <Input
                  type="number"
                  step="0.1"
                  value={pumpCapacity}
                  onChange={(e) => setPumpCapacity(Number(e.target.value))}
                />
              </div>
              <div className="grid gap-1.5">
                <Label>Max concurrent valves</Label>
                <Input
                  type="number"
                  value={maxConcurrentValves}
                  onChange={(e) => setMaxConcurrentValves(Number(e.target.value))}
                />
              </div>
            </div>
          </SectionCard>
        </div>

        {/* Col 2 */}
        <div className="space-y-6">
          <SectionCard title="Land & plots" icon={<Ruler size={16} className="text-gray-700" />}>
            <div className="flex items-end gap-3">
              <div className="flex-1 grid gap-1.5">
                <Label htmlFor="newPlot">Add plot</Label>
                <Input
                  id="newPlot"
                  placeholder="e.g., East-02"
                  value={newPlot}
                  onChange={(e) => setNewPlot(e.target.value)}
                />
              </div>
              <ActionButton type="button" onClick={addPlot}>
                <Plus size={16} />
                Add
              </ActionButton>
            </div>

            <div className="mt-4 flex flex-wrap gap-2">
              {plots.map((p) => (
                <div key={p} className="flex items-center gap-2">
                  <Pill>{p}</Pill>
                  <button
                    type="button"
                    onClick={() => removePlot(p)}
                    className="text-gray-400 hover:text-red-600"
                    aria-label={`Remove ${p}`}
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              ))}
              {plots.length === 0 && <p className="text-sm text-gray-500">No plots yet.</p>}
            </div>
          </SectionCard>

          <SectionCard title="Map defaults" icon={<Layers size={16} className="text-gray-700" />}>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-1.5">
                <Label htmlFor="basemap">Basemap</Label>
                <Select id="basemap" value={basemap} onChange={(e) => setBasemap(e.target.value as any)}>
                  <option value="satellite">Satellite</option>
                  <option value="streets">Streets</option>
                  <option value="terrain">Terrain</option>
                </Select>
              </div>
              <div className="grid gap-1.5">
                <Label htmlFor="opacity">Plot fill opacity (%)</Label>
                <Input
                  id="opacity"
                  type="number"
                  min={0}
                  max={100}
                  value={plotsOpacity}
                  onChange={(e) => setPlotsOpacity(Number(e.target.value))}
                />
              </div>
            </div>
            <div className="mt-3 text-xs text-gray-500 flex items-center gap-2">
              <Map size={14} />
              These defaults apply to the Map page for all users on this farm.
            </div>
          </SectionCard>

          <SectionCard title="Notifications" icon={<Bell size={16} className="text-gray-700" />}>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-800">Leak or abnormal flow</p>
                  <p className="text-xs text-gray-500">Instant alert when flow exceeds learned pattern.</p>
                </div>
                <Toggle checked={notifyLeaks} onChange={setNotifyLeaks} />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-800">Device offline</p>
                  <p className="text-xs text-gray-500">Alert if a sensor is offline for 15+ minutes.</p>
                </div>
                <Toggle checked={notifyOffline} onChange={setNotifyOffline} />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-800">Threshold breach</p>
                  <p className="text-xs text-gray-500">Moisture or temperature exceeds configured band.</p>
                </div>
                <Toggle checked={notifyThreshold} onChange={setNotifyThreshold} />
              </div>
              <div className="grid gap-1.5">
                <Label htmlFor="email">Notify email</Label>
                <Input id="email" type="email" value={email} onChange={(e) => setEmail(e.target.value)} />
              </div>
            </div>
          </SectionCard>
        </div>

        {/* Col 3 */}
        <div className="space-y-6">
          <SectionCard title="Sensors" icon={<Wrench size={16} className="text-gray-700" />}>
            <div className="flex items-end gap-3">
              <div className="flex-1 grid gap-1.5">
                <Label htmlFor="pair">Pair device code</Label>
                <Input
                  id="pair"
                  placeholder="e.g., ESP32-ABC123"
                  value={pairCode}
                  onChange={(e) => setPairCode(e.target.value)}
                />
              </div>
              <ActionButton type="button" onClick={addSensor}>
                <Link2 size={16} />
                Pair
              </ActionButton>
            </div>

            <div className="mt-4 border border-gray-100 rounded-xl overflow-hidden">
              <table className="min-w-full text-sm">
                <thead className="bg-gray-50 text-gray-700">
                  <tr>
                    <th className="px-3 py-2 text-left">Name</th>
                    <th className="px-3 py-2 text-left">Type</th>
                    <th className="px-3 py-2 text-left">Plot</th>
                    <th className="px-3 py-2 text-left">Device ID</th>
                    <th className="px-3 py-2 text-left">Status</th>
                    <th className="px-3 py-2"></th>
                  </tr>
                </thead>
                <tbody>
                  {sensors.map((s) => (
                    <tr key={s.id} className="border-t border-gray-100">
                      <td className="px-3 py-2">{s.name}</td>
                      <td className="px-3 py-2 capitalize">{s.type.replace("-", " ")}</td>
                      <td className="px-3 py-2">
                        <Select
                          value={s.plot}
                          onChange={(e) =>
                            setSensors((rows) =>
                              rows.map((r) => (r.id === s.id ? { ...r, plot: e.target.value } : r))
                            )
                          }
                        >
                          {plots.map((p) => (
                            <option key={p} value={p}>
                              {p}
                            </option>
                          ))}
                        </Select>
                      </td>
                      <td className="px-3 py-2">{s.deviceId}</td>
                      <td className="px-3 py-2">
                        <span
                          className={`inline-flex items-center gap-1.5 text-xs ${
                            s.status === "online"
                              ? "text-emerald-700"
                              : s.status === "offline"
                              ? "text-red-600"
                              : "text-gray-500"
                          }`}
                        >
                          <span
                            className={`h-2 w-2 rounded-full ${
                              s.status === "online"
                                ? "bg-emerald-500"
                                : s.status === "offline"
                                ? "bg-red-500"
                                : "bg-gray-300"
                            }`}
                          />
                          {s.status}
                        </span>
                      </td>
                      <td className="px-3 py-2 text-right">
                        <button
                          className="text-gray-400 hover:text-red-600"
                          onClick={() => removeSensor(s.id)}
                          aria-label={`Remove ${s.name}`}
                        >
                          <Trash2 size={16} />
                        </button>
                      </td>
                    </tr>
                  ))}
                  {sensors.length === 0 && (
                    <tr>
                      <td className="px-3 py-6 text-center text-gray-500" colSpan={6}>
                        No sensors yet.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </SectionCard>

          <SectionCard title="API & webhooks" icon={<Link2 size={16} className="text-gray-700" />}>
            <div className="grid gap-4">
              <div className="grid gap-1.5">
                <Label htmlFor="webhook">Webhook URL (events)</Label>
                <Input
                  id="webhook"
                  placeholder="https://example.com/webhooks/farmti"
                  value={webhookUrl}
                  onChange={(e) => setWebhookUrl(e.target.value)}
                />
              </div>
              <div className="grid gap-1.5">
                <Label>API key</Label>
                {apiKey ? (
                  <div className="flex items-center gap-3">
                    <Input readOnly value={apiKey} />
                    <GhostButton type="button" onClick={() => setApiKey(null)}>
                      Revoke
                    </GhostButton>
                  </div>
                ) : (
                  <ActionButton type="button" onClick={generateApiKey}>
                    Generate key
                  </ActionButton>
                )}
              </div>
            </div>
          </SectionCard>
        </div>
      </div>

      {/* Footer bar */}
      <div className="sticky bottom-0 left-0 right-0 border-t border-gray-200 bg-white/80 backdrop-blur supports-[backdrop-filter]:bg-white/60">
        <div className="max-w-screen-2xl mx-auto px-6 py-3 flex items-center justify-end gap-3">
          <GhostButton type="button" onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}>
            Back to top
          </GhostButton>
          <ActionButton type="button" onClick={handleSaveAll}>
            <Save size={16} />
            Save changes
          </ActionButton>
        </div>
      </div>
    </div>
  );
};

export default Configuration;
