"use client";

import React from 'react';
import { 
  Droplets, 
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Wrench
} from 'lucide-react';
import Link from 'next/link';

const ComingSoonFeatures: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full text-center">
        {/* Icon */}
        <div className="mb-8">
          <div className="w-20 h-20 bg-[#21c45d] rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
            <Wrench className="w-10 h-10 text-white" />
          </div>
        </div>

        {/* Content */}
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Feature Coming Soon
        </h1>
        
        <p className="text-lg text-gray-600 mb-8 leading-relaxed">
          We're working hard to bring you exciting new features. 
          This section will be available soon!
        </p>

        {/* Status */}
        <div className="bg-white rounded-lg p-4 border border-gray-200 mb-8 shadow-sm">
          <div className="flex items-center justify-center space-x-2 text-[#21c45d]">
            <Clock className="w-5 h-5" />
            <span className="font-medium">In Development</span>
          </div>
        </div>

        {/* Back Button */}
        <Link href="/dashboard">
          <button className="cursor-pointer inline-flex items-center px-6 py-3 bg-[#21c45d] text-white font-medium rounded-lg hover:bg-[#1aa052] transition-colors duration-200">
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Dashboard
          </button>
        </Link>
      </div>

      {/* Footer */}
      <div className="mt-16 text-center">
        <div className="flex items-center justify-center space-x-2 text-gray-400 mb-2">
          <Droplets className="w-4 h-4" />
          <span className="text-sm">Farmti</span>
        </div>
        <p className="text-xs text-gray-500">Smart Farm Management</p>
      </div>
    </div>
  );
};

export default ComingSoonFeatures;
