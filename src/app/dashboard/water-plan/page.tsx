"use client";

import React, { useMemo, useState } from "react";
import {
  CalendarClock,
  Plus,
  Trash2,
  Save,
  Droplets,
  Timer,
  Gauge,
  Download,
  Play,
  Pause,
} from "lucide-react";

/* ---------- Types ---------- */
type PlanItem = {
  id: string;
  plot: string;
  start: string;        // "HH:MM"
  durationMin: number;  // minutes
  flowLpm: number;      // liters per minute
  repeat: "none" | "daily" | "odd" | "even";
  enabled: boolean;
};

/* ---------- Constants ---------- */
const PRIMARY = "#21c45d";

/* ---------- UI atoms ---------- */
const Card: React.FC<{ children: React.ReactNode; className?: string; title?: string; icon?: React.ReactNode }> = ({
  children, className, title, icon,
}) => (
  <div className={`bg-white rounded-2xl border border-gray-100 shadow-sm ${className || ""}`}>
    {title && (
      <div className="px-5 py-4 border-b border-gray-100 flex items-center gap-2">
        {icon}
        <h3 className="text-sm font-semibold text-gray-900">{title}</h3>
      </div>
    )}
    <div className="p-5">{children}</div>
  </div>
);

const Input = (p: React.InputHTMLAttributes<HTMLInputElement>) => (
  <input
    {...p}
    className="w-full rounded-xl border border-gray-200 bg-white px-3 py-2 text-sm text-gray-900 outline-none focus:ring-2 focus:ring-offset-0"
  />
);

const Select = (p: React.SelectHTMLAttributes<HTMLSelectElement>) => (
  <select
    {...p}
    className="w-full rounded-xl border border-gray-200 bg-white px-3 py-2 text-sm text-gray-900 outline-none focus:ring-2 focus:ring-offset-0"
  />
);

const Toggle = ({ checked, onChange }: { checked: boolean; onChange: (v: boolean) => void }) => (
  <button
    type="button"
    onClick={() => onChange(!checked)}
    className="h-6 w-11 rounded-full transition-colors bg-gray-200 relative"
    style={{ backgroundColor: checked ? PRIMARY : undefined }}
    aria-pressed={checked}
  >
    <span
      className={`absolute top-0.5 h-5 w-5 rounded-full bg-white shadow transition-transform ${
        checked ? "translate-x-6" : "translate-x-1"
      }`}
    />
  </button>
);

const InfoPill: React.FC<{ label: string; value: string; ok: boolean }> = ({ label, value, ok }) => (
  <div
    className={`flex items-center justify-between rounded-xl px-3 py-2 text-sm border ${
      ok
        ? "bg-emerald-50 border-emerald-200 text-emerald-700"
        : "bg-amber-50 border-amber-200 text-amber-800"
    }`}
  >
    <span className="text-xs">{label}</span>
    <span className="font-semibold">{value}</span>
  </div>
);

/* ---------- Helpers ---------- */
const toMinutes = (hhmm: string) => {
  const [h, m] = hhmm.split(":").map(Number);
  return h * 60 + m;
};
const overlaps = (aStart: number, aEnd: number, bStart: number, bEnd: number) =>
  Math.max(aStart, bStart) < Math.min(aEnd, bEnd);

function condenseMinuteWindows(mins: number[]) {
  if (mins.length === 0) return [] as [number, number][];
  const sorted = [...mins].sort((a, b) => a - b);
  const out: [number, number][] = [];
  let start = sorted[0], prev = sorted[0];
  for (let i = 1; i < sorted.length; i++) {
    const m = sorted[i];
    if (m === prev + 1) prev = m;
    else { out.push([start, prev + 1]); start = m; prev = m; }
  }
  out.push([start, prev + 1]);
  return out;
}
function minToHHMM(m: number) {
  const h = Math.floor(m / 60), mm = m % 60;
  return `${String(h).padStart(2, "0")}:${String(mm).padStart(2, "0")}`;
}
function formatWindows(wins: [number, number][]) {
  if (!wins.length) return "—";
  return wins.map(([a, b]) => `${minToHHMM(a)}–${minToHHMM(b)}`).join(", ");
}
const minuteWindowsSet = (wins: [number, number][]) => {
  const set = new Set<number>();
  wins.forEach(([a, b]) => { for (let m = a; m < b; m++) set.add(m); });
  return set;
};

/* ---------- Page ---------- */
export default function WaterPlanPage() {
  // Constraints
  const [pumpCapacityM3h, setPumpCapacityM3h] = useState(2.5);   // m³/h
  const [maxConcurrent, setMaxConcurrent] = useState(3);

  // Available plots
  const [plots] = useState<string[]>(["North-01", "North-02", "South-01", "East-01"]);

  // Plan items
  const [items, setItems] = useState<PlanItem[]>([
    { id: "1", plot: "North-01", start: "06:00", durationMin: 30, flowLpm: 18, repeat: "daily", enabled: true },
    { id: "2", plot: "South-01", start: "06:15", durationMin: 25, flowLpm: 15, repeat: "daily", enabled: true },
  ]);

  // Simulation flag
  const [simRunning, setSimRunning] = useState(false);

  // Totals
  const totals = useMemo(() => {
    const perPlot = new Map<string, number>();
    let sum = 0;
    for (const it of items.filter(i => i.enabled)) {
      const liters = Math.max(0, it.durationMin) * Math.max(0, it.flowLpm);
      perPlot.set(it.plot, (perPlot.get(it.plot) || 0) + liters);
      sum += liters;
    }
    return { perPlot, sum };
  }, [items]);

  // Concurrency violations
  const violations = useMemo(() => {
    const enabled = items.filter(i => i.enabled);
    const buckets: number[] = Array(24 * 60).fill(0);
    for (const it of enabled) {
      const s = toMinutes(it.start);
      const e = s + Math.max(0, it.durationMin);
      for (let t = s; t < e && t < 24 * 60; t++) buckets[t] += 1;
    }
    const worst = Math.max(0, ...buckets);
    const times = buckets
      .map((v, i) => ({ v, i }))
      .filter(x => x.v > maxConcurrent)
      .map(x => x.i);
    return { worst, any: times.length > 0, windows: condenseMinuteWindows(times) };
  }, [items, maxConcurrent]);

  // Pump capacity violations
  const flowViolations = useMemo(() => {
    const capLpm = (Math.max(0, pumpCapacityM3h) * 1000) / 60;
    const enabled = items.filter(i => i.enabled);
    const minutes = new Array(24 * 60).fill(0).map((_, i) => {
      const active = enabled.filter(it => {
        const s = toMinutes(it.start);
        const e = s + Math.max(0, it.durationMin);
        return overlaps(s, e, i, i + 1);
      });
      const totalLpm = active.reduce((s, it) => s + Math.max(0, it.flowLpm), 0);
      return totalLpm;
    });
    const worst = Math.max(0, ...minutes);
    const badTimes = minutes
      .map((v, i) => ({ v, i }))
      .filter(x => x.v > capLpm)
      .map(x => x.i);
    return { capLpm, worst, any: badTimes.length > 0, windows: condenseMinuteWindows(badTimes) };
  }, [items, pumpCapacityM3h]);

  // Minute sets for per-row badges
  const violationMinutes = minuteWindowsSet(violations.windows);
  const flowViolationMinutes = minuteWindowsSet(flowViolations.windows);

  // Actions
  const addItem = () => {
    setItems(s => [
      ...s,
      {
        id: crypto.randomUUID(),
        plot: plots[0] || "Plot-1",
        start: "06:00",
        durationMin: 20,
        flowLpm: 12,
        repeat: "daily",
        enabled: true,
      },
    ]);
  };
  const removeItem = (id: string) => setItems(s => s.filter(i => i.id !== id));
  const savePlan = () => {
    console.info("WATER_PLAN_SAVE", { pumpCapacityM3h, maxConcurrent, items });
    alert("Water plan saved.");
  };
  const exportCSV = () => {
    if (!items.length) return;
    const rows = items.map(i => ({
      plot: i.plot,
      start: i.start,
      duration_min: i.durationMin,
      flow_lpm: i.flowLpm,
      repeat: i.repeat,
      enabled: i.enabled ? "yes" : "no",
    }));
    const headers = Object.keys(rows[0]);
    const csv =
      [headers.join(",")]
        .concat(rows.map(r => headers.map(h => JSON.stringify((r as any)[h] ?? "")).join(",")))
        .join("\n");
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url; a.download = "water-plan.csv"; a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Water Plan</h2>
          <p className="text-sm text-gray-600">Schedule irrigations by plot with pump and concurrency limits.</p>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <button
            className="inline-flex items-center gap-2 rounded-xl px-4 py-2 text-sm font-semibold text-white"
            style={{ background: PRIMARY }}
            onClick={savePlan}
          >
            <Save size={16} /> Save plan
          </button>
          <button
            className="inline-flex items-center gap-2 rounded-xl px-4 py-2 text-sm font-semibold border border-gray-200 bg-white hover:bg-gray-50"
            onClick={exportCSV}
          >
            <Download size={16} /> Export CSV
          </button>
          <button
            className="inline-flex items-center gap-2 rounded-xl px-4 py-2 text-sm font-semibold border border-gray-200 bg-white hover:bg-gray-50"
            onClick={() => {
              const blob = new Blob([JSON.stringify({ pumpCapacityM3h, maxConcurrent, items }, null, 2)], { type: "application/json" });
              const url = URL.createObjectURL(blob); const a = document.createElement("a");
              a.href = url; a.download = "water-plan.json"; a.click(); URL.revokeObjectURL(url);
            }}
          >
            Export JSON
          </button>
          <label className="inline-flex items-center gap-2 rounded-xl px-4 py-2 text-sm font-semibold border border-gray-200 bg-white hover:bg-gray-50 cursor-pointer">
            Import JSON
            <input
              type="file"
              accept="application/json"
              className="hidden"
              onChange={async (e) => {
                const f = e.target.files?.[0]; if (!f) return;
                const text = await f.text();
                try {
                  const data = JSON.parse(text);
                  if (Array.isArray(data.items)) setItems(data.items);
                  if (typeof data.pumpCapacityM3h === "number") setPumpCapacityM3h(Math.max(0, data.pumpCapacityM3h));
                  if (typeof data.maxConcurrent === "number") setMaxConcurrent(Math.max(1, Math.floor(data.maxConcurrent)));
                  alert("Plan imported.");
                } catch { alert("Invalid JSON."); }
              }}
            />
          </label>
        </div>
      </div>

      {/* Constraints + Summary + Simulation */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        <Card title="Constraints" icon={<Gauge size={16} className="text-gray-700" />}>
          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-1.5">
              <label className="text-sm font-medium text-gray-700">Pump capacity (m³/h)</label>
              <Input
                type="number"
                step="0.1"
                value={pumpCapacityM3h}
                onChange={(e) => setPumpCapacityM3h(Math.max(0, Number(e.target.value) || 0))}
              />
              <p className="text-xs text-gray-500">≈ {(Math.max(0, pumpCapacityM3h) * 1000 / 60).toFixed(0)} L/min</p>
            </div>
            <div className="grid gap-1.5">
              <label className="text-sm font-medium text-gray-700">Max concurrent valves</label>
              <Input
                type="number"
                value={maxConcurrent}
                onChange={(e) => setMaxConcurrent(Math.max(1, Math.floor(Number(e.target.value) || 1)))}
              />
            </div>
          </div>
          <div className="mt-4 grid grid-cols-2 gap-4">
            <InfoPill label="Concurrency" value={`${violations.worst} active max`} ok={violations.worst <= maxConcurrent} />
            <InfoPill
              label="Flow peak"
              value={`${flowViolations.worst.toFixed(0)} / ${flowViolations.capLpm.toFixed(0)} L/min`}
              ok={flowViolations.worst <= flowViolations.capLpm}
            />
          </div>
          {(violations.any || flowViolations.any) && (
            <div className="mt-3 rounded-xl border border-amber-200 bg-amber-50 p-3 text-sm text-amber-800">
              <p className="font-semibold mb-1">Conflicts detected</p>
              {violations.any && <p>Concurrency exceeds limit at: {formatWindows(violations.windows)}</p>}
              {flowViolations.any && <p>Total flow exceeds pump capacity at: {formatWindows(flowViolations.windows)}</p>}
            </div>
          )}
        </Card>

        <Card title="Totals" icon={<Droplets size={16} className="text-gray-700" />}>
          <div className="grid grid-cols-2 gap-4">
            <div className="rounded-xl border border-gray-200 p-3">
              <p className="text-xs text-gray-500">Daily total</p>
              <p className="text-xl font-bold text-gray-900">{Math.round(totals.sum).toLocaleString()} L</p>
            </div>
            <div className="rounded-xl border border-gray-200 p-3">
              <p className="text-xs text-gray-500">Avg per item</p>
              <p className="text-xl font-bold text-gray-900">
                {items.length ? Math.round(totals.sum / items.length).toLocaleString() : 0} L
              </p>
            </div>
          </div>
          <div className="mt-4">
            <table className="min-w-full text-sm border border-gray-100 rounded-xl overflow-hidden">
              <thead className="bg-gray-50 text-gray-700">
                <tr>
                  <th className="px-3 py-2 text-left">Plot</th>
                  <th className="px-3 py-2 text-right">Liters/day</th>
                </tr>
              </thead>
              <tbody>
                {Array.from(totals.perPlot.entries()).map(([plot, L]) => (
                  <tr key={plot} className="border-t border-gray-100">
                    <td className="px-3 py-2">{plot}</td>
                    <td className="px-3 py-2 text-right">{Math.round(L).toLocaleString()}</td>
                  </tr>
                ))}
                {totals.perPlot.size === 0 && (
                  <tr><td colSpan={2} className="px-3 py-3 text-center text-gray-500">No items</td></tr>
                )}
              </tbody>
            </table>
          </div>
        </Card>

        <Card title="Simulation" icon={<CalendarClock size={16} className="text-gray-700" />}>
          <div className="flex items-center gap-2">
            <button
              className="inline-flex items-center gap-2 rounded-xl px-4 py-2 text-sm font-semibold text-white"
              style={{ background: PRIMARY }}
              onClick={() => setSimRunning(true)}
            >
              <Play size={16} /> Start
            </button>
            <button
              className="inline-flex items-center gap-2 rounded-xl px-4 py-2 text-sm font-semibold border border-gray-200 bg-white hover:bg-gray-50"
              onClick={() => setSimRunning(false)}
            >
              <Pause size={16} /> Stop
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-3">
            Simulates overlap and pump load across the next 24h. Uses minute buckets.
          </p>
          <div className="mt-4">
            <Timeline items={items} />
          </div>
        </Card>
      </div>

      {/* Planner table */}
      <Card title="Plan items" icon={<Timer size={16} className="text-gray-700" />}>
        <div className="flex items-center justify-between mb-3">
          <div className="text-sm text-gray-600">Define start time, duration, flow, and repeat rules per plot.</div>
          <button
            className="inline-flex items-center gap-2 rounded-xl px-4 py-2 text-sm font-semibold text-white"
            style={{ background: PRIMARY }}
            onClick={addItem}
          >
            <Plus size={16} /> Add item
          </button>
        </div>

        {/* Presets */}
        <div className="flex flex-wrap items-center gap-2 mb-4">
          <button
            className="rounded-xl border border-gray-200 px-3 py-1.5 text-xs font-medium hover:bg-gray-50"
            onClick={() => setItems(s => s.map(x => ({ ...x, start: "06:00" })))}
          >
            Set all start to 06:00
          </button>
          <button
            className="rounded-xl border border-gray-200 px-3 py-1.5 text-xs font-medium hover:bg-gray-50"
            onClick={() => setItems(s => s.map((x, i) => ({ ...x, start: minToHHMM(360 + i * 15) })))}
          >
            Stagger every 15m
          </button>
          <button
            className="rounded-xl border border-gray-200 px-3 py-1.5 text-xs font-medium hover:bg-gray-50"
            onClick={() => setItems(s => s.map(x => ({ ...x, repeat: "odd" })))}
          >
            Odd days
          </button>
          <button
            className="rounded-xl border border-gray-200 px-3 py-1.5 text-xs font-medium hover:bg-gray-50"
            onClick={() => setItems(s => s.map(x => ({ ...x, repeat: "even" })))}
          >
            Even days
          </button>
        </div>

        <div className="rounded-xl border border-gray-100 overflow-x-auto">
          <table className="min-w-full text-sm">
            <thead className="bg-gray-50 text-gray-700">
              <tr>
                <th className="px-3 py-2 text-left">Enabled</th>
                <th className="px-3 py-2 text-left">Plot</th>
                <th className="px-3 py-2 text-left">Start</th>
                <th className="px-3 py-2 text-left">Duration (min)</th>
                <th className="px-3 py-2 text-left">Flow (L/min)</th>
                <th className="px-3 py-2 text-left">Repeat</th>
                <th className="px-3 py-2 text-right">Liters</th>
                <th className="px-3 py-2"></th>
              </tr>
            </thead>
            <tbody>
              {items.map((it) => {
                const sMin = toMinutes(it.start);
                const eMin = sMin + Math.max(0, it.durationMin);
                const minutesInRow = Array.from({ length: Math.max(0, eMin - sMin) }, (_, k) => sMin + k);
                const hasConcurrencyHit = minutesInRow.some(m => violationMinutes.has(m));
                const hasFlowHit = minutesInRow.some(m => flowViolationMinutes.has(m));
                const liters = Math.max(0, it.durationMin) * Math.max(0, it.flowLpm);

                return (
                  <tr key={it.id} className="border-t border-gray-100">
                    <td className="px-3 py-2">
                      <div className="flex items-center gap-2">
                        <Toggle
                          checked={it.enabled}
                          onChange={(v) => setItems(s => s.map(x => x.id === it.id ? { ...x, enabled: v } : x))}
                        />
                        {(hasConcurrencyHit || hasFlowHit) && (
                          <span
                            className={`px-2 py-0.5 rounded-full text-[10px] font-semibold ${
                              hasFlowHit ? "bg-red-100 text-red-700" : "bg-amber-100 text-amber-700"
                            }`}
                          >
                            {hasFlowHit ? "Flow cap" : "Concurrent"}
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-3 py-2">
                      <Select
                        value={it.plot}
                        onChange={(e) => setItems(s => s.map(x => x.id === it.id ? { ...x, plot: e.target.value } : x))}
                      >
                        {plots.map(p => <option key={p} value={p}>{p}</option>)}
                      </Select>
                    </td>
                    <td className="px-3 py-2">
                      <Input
                        type="time"
                        value={it.start}
                        onChange={(e) => setItems(s => s.map(x => x.id === it.id ? { ...x, start: e.target.value } : x))}
                      />
                    </td>
                    <td className="px-3 py-2">
                      <Input
                        type="number"
                        min={0}
                        value={it.durationMin}
                        onChange={(e) =>
                          setItems(s => s.map(x => x.id === it.id
                            ? { ...x, durationMin: Math.max(0, Math.floor(Number(e.target.value) || 0)) }
                            : x))
                        }
                      />
                    </td>
                    <td className="px-3 py-2">
                      <Input
                        type="number"
                        min={0}
                        step={1}
                        value={it.flowLpm}
                        onChange={(e) =>
                          setItems(s => s.map(x => x.id === it.id
                            ? { ...x, flowLpm: Math.max(0, Math.round(Number(e.target.value) || 0)) }
                            : x))
                        }
                      />
                    </td>
                    <td className="px-3 py-2">
                      <Select
                        value={it.repeat}
                        onChange={(e) =>
                          setItems(s => s.map(x => x.id === it.id
                            ? { ...x, repeat: e.target.value as PlanItem["repeat"] }
                            : x))
                        }
                      >
                        <option value="none">None</option>
                        <option value="daily">Daily</option>
                        <option value="odd">Odd days</option>
                        <option value="even">Even days</option>
                      </Select>
                    </td>
                    <td className="px-3 py-2 text-right">{Math.round(liters).toLocaleString()} L</td>
                    <td className="px-3 py-2 text-right">
                      <div className="flex items-center justify-end gap-2">
                        <button
                          className="text-gray-400 hover:text-gray-700"
                          onClick={() =>
                            setItems(s => {
                              const copy = { ...it, id: crypto.randomUUID() };
                              return [...s, copy];
                            })
                          }
                          aria-label="Duplicate"
                          title="Duplicate"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M8 8V5a2 2 0 0 1 2-2h9v13a2 2 0 0 1-2 2h-3v3H5a2 2 0 0 1-2-2v-9zM8 10H5v9h9v-3H10a2 2 0 0 1-2-2z"/>
                          </svg>
                        </button>
                        <button
                          className="text-gray-400 hover:text-red-600"
                          onClick={() => removeItem(it.id)}
                          aria-label="Remove"
                          title="Remove"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
              {items.length === 0 && (
                <tr><td colSpan={8} className="px-3 py-6 text-center text-gray-500">No plan items.</td></tr>
              )}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
}

/* ---------- Timeline (visual) ---------- */
function Timeline({ items }: { items: PlanItem[] }) {
  const tracks = layoutTracks(items.filter(i => i.enabled));
  return (
    <div className="w-full">
      <div className="flex justify-between text-xs text-gray-500 mb-1">
        {["00:00","04:00","08:00","12:00","16:00","20:00","24:00"].map(t => <span key={t}>{t}</span>)}
      </div>
      <div className="relative w-full rounded-xl border border-gray-200 bg-gray-50 h-28 overflow-hidden">
        {tracks.map((row, rIdx) => (
          <div key={rIdx} className="absolute left-0 right-0" style={{ top: rIdx * 28 + 8 }}>
            {row.map((b, i) => {
              const s = toMinutes(b.start);
              const w = (Math.max(0, b.durationMin) / (24 * 60)) * 100;
              const l = (s / (24 * 60)) * 100;
              return (
                <div
                  key={i}
                  className="absolute h-6 rounded-lg text-xs text-white flex items-center px-2"
                  style={{
                    width: `${w}%`,
                    left: `${l}%`,
                    background: PRIMARY,
                    boxShadow: "0 4px 12px rgba(33,196,93,0.25)",
                  }}
                  title={`${b.plot} • ${b.start} (${b.durationMin}m)`}
                >
                  <span className="truncate">{b.plot}</span>
                </div>
              );
            })}
          </div>
        ))}
      </div>
      <p className="mt-2 text-xs text-gray-500">Bars show enabled items across 24h; stacked rows avoid overlap.</p>
    </div>
  );
}

/* ---------- Layout for timeline ---------- */
function layoutTracks(items: PlanItem[]) {
  const sorted = [...items].sort((a, b) => toMinutes(a.start) - toMinutes(b.start));
  const rows: PlanItem[][] = [];
  for (const it of sorted) {
    const s = toMinutes(it.start), e = s + Math.max(0, it.durationMin);
    let placed = false;
    for (const row of rows) {
      const clash = row.some(r => overlaps(s, e, toMinutes(r.start), toMinutes(r.start) + Math.max(0, r.durationMin)));
      if (!clash) { row.push(it); placed = true; break; }
    }
    if (!placed) rows.push([it]);
  }
  return rows;
}
