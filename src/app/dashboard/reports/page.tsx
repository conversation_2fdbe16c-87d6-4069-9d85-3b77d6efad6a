"use client";

import React, { useMemo, useState } from "react";
import {
  Calendar,
  Filter,
  Download,
  FileSpreadsheet,
  FileText,
  Droplets,
  Thermometer,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  TrendingUp,
} from "lucide-react";
import {
  <PERSON>Chart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
} from "recharts";

const PRIMARY = "#21c45d";

/* ---- Mock data ---- */
const dailyUsage = [
  { day: "Mon", usage: 1200, target: 1100 },
  { day: "Tue", usage: 1350, target: 1200 },
  { day: "Wed", usage: 980, target: 1150 },
  { day: "Thu", usage: 1100, target: 1000 },
  { day: "Fri", usage: 1280, target: 1200 },
  { day: "Sat", usage: 1320, target: 1250 },
  { day: "Sun", usage: 1400, target: 1300 },
];

const plotUsage = [
  { plot: "North-01", liters: 5200 },
  { plot: "North-02", liters: 4100 },
  { plot: "South-01", liters: 6100 },
  { plot: "East-01", liters: 4600 },
];

const moistureBuckets = [
  { name: "0–20%", value: 3 },
  { name: "20–40%", value: 7 },
  { name: "40–60%", value: 12 },
  { name: "60–80%", value: 9 },
  { name: "80–100%", value: 4 },
];

const alerts = [
  { ts: "2025-09-14 10:12", type: "Leak suspected", plot: "South-01", status: "resolved" },
  { ts: "2025-09-14 08:51", type: "Low moisture", plot: "North-02", status: "ack" },
  { ts: "2025-09-13 19:04", type: "Gateway offline", plot: "—", status: "resolved" },
  { ts: "2025-09-13 07:22", type: "Pressure drop", plot: "East-01", status: "open" },
];

/* ---- Small atoms ---- */
const Label = ({ htmlFor, children }: { htmlFor?: string; children: React.ReactNode }) => (
  <label htmlFor={htmlFor} className="text-sm font-medium text-gray-700">
    {children}
  </label>
);

const Input = (p: React.InputHTMLAttributes<HTMLInputElement>) => (
  <input
    {...p}
    className="w-full rounded-xl border border-gray-200 bg-white px-3 py-2 text-sm text-gray-900 outline-none focus:ring-2"
    style={{ boxShadow: "none" }}
  />
);

const Select = (p: React.SelectHTMLAttributes<HTMLSelectElement>) => (
  <select
    {...p}
    className="w-full rounded-xl border border-gray-200 bg-white px-3 py-2 text-sm text-gray-900 outline-none focus:ring-2"
  />
);

const Card = ({ children }: { children: React.ReactNode }) => (
  <div className="bg-white rounded-2xl border border-gray-100 shadow-sm">{children}</div>
);

/* ---- Export helpers ---- */
function downloadCSV(filename: string, rows: Record<string, any>[]) {
  if (!rows?.length) return;
  const headers = Object.keys(rows[0]);
  const csv =
    [headers.join(",")]
      .concat(rows.map(r => headers.map(h => JSON.stringify(r[h] ?? "")).join(",")))
      .join("\n");
  const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = filename;
  a.click();
  URL.revokeObjectURL(url);
}

/* ---- Page ---- */
export default function ReportsPage() {
  const [range, setRange] = useState<{ from: string; to: string }>({
    from: "2025-09-08",
    to: "2025-09-14",
  });
  const [plot, setPlot] = useState<string>("all");
  const [compare, setCompare] = useState<boolean>(false);

  const kpis = useMemo(() => {
    const total = dailyUsage.reduce((s, d) => s + d.usage, 0);
    const target = dailyUsage.reduce((s, d) => s + d.target, 0);
    const variance = ((total - target) / target) * 100;
    return [
      {
        title: "Total Water",
        value: `${total.toLocaleString()} L`,
        change: `${variance >= 0 ? "+" : ""}${variance.toFixed(1)}% vs target`,
        icon: <Droplets className="w-5 h-5 text-white" />,
        color: PRIMARY,
        up: variance >= 0,
      },
      {
        title: "Avg Moisture",
        value: "76.0%",
        change: "+2.4% vs last week",
        icon: <Thermometer className="w-5 h-5 text-white" />,
        color: "#10b981",
        up: true,
      },
      {
        title: "System Uptime",
        value: "99.1%",
        change: "+0.6% vs last week",
        icon: <Activity className="w-5 h-5 text-white" />,
        color: "#22c55e",
        up: true,
      },
      {
        title: "Efficiency Score",
        value: "82",
        change: "-1.8 vs last week",
        icon: <TrendingUp className="w-5 h-5 text-white" />,
        color: "#f59e0b",
        up: false,
      },
    ];
  }, []);

  return (
    <div className="p-6 space-y-6">
      {/* Header + actions */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Reports</h2>
          <p className="text-sm text-gray-600">Water, moisture, uptime, and alerts for the selected period.</p>
        </div>
        <div className="flex items-center gap-2">
          <button
            className="inline-flex items-center gap-2 rounded-xl px-4 py-2 text-sm font-semibold text-white"
            style={{ background: PRIMARY }}
            onClick={() => downloadCSV("water-usage.csv", dailyUsage)}
          >
            <FileSpreadsheet size={16} />
            Export CSV
          </button>
          <button
            className="inline-flex items-center gap-2 rounded-xl px-4 py-2 text-sm font-semibold border border-gray-200 bg-white hover:bg-gray-50"
            onClick={() => window.print()}
          >
            <FileText size={16} />
            Print/PDF
          </button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <div className="p-5 grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="grid gap-1.5">
            <Label htmlFor="from">
              <span className="inline-flex items-center gap-1"><Calendar size={14}/> From</span>
            </Label>
            <Input id="from" type="date" value={range.from} onChange={(e) => setRange(r => ({ ...r, from: e.target.value }))} />
          </div>
          <div className="grid gap-1.5">
            <Label htmlFor="to">
              <span className="inline-flex items-center gap-1"><Calendar size={14}/> To</span>
            </Label>
            <Input id="to" type="date" value={range.to} onChange={(e) => setRange(r => ({ ...r, to: e.target.value }))} />
          </div>
          <div className="grid gap-1.5">
            <Label htmlFor="plot">
              <span className="inline-flex items-center gap-1"><Filter size={14}/> Plot</span>
            </Label>
            <Select id="plot" value={plot} onChange={(e) => setPlot(e.target.value)}>
              <option value="all">All plots</option>
              <option value="North-01">North-01</option>
              <option value="North-02">North-02</option>
              <option value="South-01">South-01</option>
              <option value="East-01">East-01</option>
            </Select>
          </div>
          <div className="grid gap-1.5">
            <Label>Comparison</Label>
            <button
              onClick={() => setCompare(s => !s)}
              className="rounded-xl border border-gray-200 px-3 py-2 text-sm font-medium bg-white hover:bg-gray-50"
              style={compare ? { borderColor: PRIMARY, boxShadow: `inset 0 0 0 1px ${PRIMARY}` } : {}}
            >
              {compare ? "Comparing vs last period" : "Enable compare"}
            </button>
          </div>
        </div>
      </Card>

      {/* KPI cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
        {kpis.map((k) => (
          <Card key={k.title}>
            <div className="p-5 flex items-center gap-4">
              <div
                className="h-10 w-10 rounded-xl flex items-center justify-center text-white"
                style={{ background: k.color }}
              >
                {k.icon}
              </div>
              <div className="flex-1">
                <p className="text-xs text-gray-500">{k.title}</p>
                <div className="flex items-baseline gap-2">
                  <span className="text-xl font-bold text-gray-900">{k.value}</span>
                  <span className={`text-xs font-semibold ${k.up ? "text-emerald-600" : "text-red-600"} inline-flex items-center gap-1`}>
                    {k.up ? <ArrowUpRight size={12}/> : <ArrowDownRight size={12}/>}
                    {k.change}
                  </span>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* Daily water usage */}
        <Card>
          <div className="p-5">
            <h3 className="text-lg font-semibold text-gray-900">Daily water usage vs target</h3>
            <p className="text-sm text-gray-600 mb-4">Sum of liters per day for the selected plots.</p>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={dailyUsage}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis dataKey="day" stroke="#6b7280" fontSize={12} />
                  <YAxis stroke="#6b7280" fontSize={12} />
                  <Tooltip contentStyle={{ backgroundColor: "white", border: "1px solid #e5e7eb", borderRadius: 12 }} />
                  <Line type="monotone" dataKey="usage" stroke={PRIMARY} strokeWidth={3} dot={{ r: 4, fill: PRIMARY }} name="Usage" />
                  <Line type="monotone" dataKey="target" stroke="#9ca3af" strokeDasharray="5 5" strokeWidth={2} dot={{ r: 3, fill: "#9ca3af" }} name="Target" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        </Card>

        {/* Usage by plot */}
        <Card>
          <div className="p-5">
            <h3 className="text-lg font-semibold text-gray-900">Water usage by plot</h3>
            <p className="text-sm text-gray-600 mb-4">Total liters per plot.</p>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={plotUsage}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis dataKey="plot" stroke="#6b7280" fontSize={12} />
                  <YAxis stroke="#6b7280" fontSize={12} />
                  <Tooltip contentStyle={{ backgroundColor: "white", border: "1px solid #e5e7eb", borderRadius: 12 }} />
                  <Bar dataKey="liters" fill={PRIMARY} radius={[8,8,0,0]} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
        </Card>
      </div>

      {/* Distribution + Alerts */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* Moisture distribution */}
        <Card>
          <div className="p-5">
            <h3 className="text-lg font-semibold text-gray-900">Moisture distribution</h3>
            <p className="text-sm text-gray-600 mb-4">Number of sensor-readings per bucket.</p>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie data={moistureBuckets} dataKey="value" nameKey="name" innerRadius={60} outerRadius={100} paddingAngle={4}>
                    {moistureBuckets.map((_, i) => {
                      const palette = [PRIMARY, "#34d399", "#a7f3d0", "#86efac", "#bbf7d0"];
                      return <Cell key={i} fill={palette[i % palette.length]} />;
                    })}
                  </Pie>
                  <Tooltip contentStyle={{ backgroundColor: "white", border: "1px solid #e5e7eb", borderRadius: 12 }} />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
        </Card>

        {/* Alerts table */}
        <Card>
          <div className="p-5">
            <h3 className="text-lg font-semibold text-gray-900">Alerts</h3>
            <p className="text-sm text-gray-600 mb-4">Key events during the selected period.</p>
            <div className="rounded-xl border border-gray-100 overflow-hidden">
              <table className="min-w-full text-sm">
                <thead className="bg-gray-50 text-gray-700">
                  <tr>
                    <th className="px-3 py-2 text-left">Timestamp</th>
                    <th className="px-3 py-2 text-left">Type</th>
                    <th className="px-3 py-2 text-left">Plot</th>
                    <th className="px-3 py-2 text-left">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {alerts.map((a, idx) => (
                    <tr key={idx} className="border-t border-gray-100">
                      <td className="px-3 py-2">{a.ts}</td>
                      <td className="px-3 py-2">{a.type}</td>
                      <td className="px-3 py-2">{a.plot}</td>
                      <td className="px-3 py-2">
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-semibold ${
                            a.status === "resolved"
                              ? "bg-emerald-100 text-emerald-700"
                              : a.status === "ack"
                              ? "bg-yellow-100 text-yellow-700"
                              : "bg-red-100 text-red-700"
                          }`}
                        >
                          {a.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="mt-4 flex items-center gap-2">
              <button
                className="inline-flex items-center gap-2 rounded-xl px-4 py-2 text-sm font-semibold text-white"
                style={{ background: PRIMARY }}
                onClick={() => downloadCSV("alerts.csv", alerts)}
              >
                <Download size={16} />
                Export alerts CSV
              </button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
