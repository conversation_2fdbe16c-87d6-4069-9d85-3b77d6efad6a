"use client";

import React, { useState } from "react";
import {
  User,
  Building2,
  <PERSON>,
  Shield,
  KeyRound,
  CreditCard,
  Link2,
  Download,
  Trash2,
  Save,
  Mail,
  Phone,
  Globe,
  Lock,
  QrCode,
} from "lucide-react";

const PRIMARY = "#21c45d";

/* UI atoms */
const Section = ({ title, icon, children }: { title: string; icon: React.ReactNode; children: React.ReactNode }) => (
  <div className="bg-white rounded-2xl border border-gray-100 shadow-sm">
    <div className="px-5 py-4 border-b border-gray-100 flex items-center gap-2">
      {icon}
      <h3 className="text-sm font-semibold text-gray-900">{title}</h3>
    </div>
    <div className="p-5">{children}</div>
  </div>
);

const Label = ({ htmlFor, children }: { htmlFor?: string; children: React.ReactNode }) => (
  <label htmlFor={htmlFor} className="text-sm font-medium text-gray-700">
    {children}
  </label>
);

const Input = (props: React.InputHTMLAttributes<HTMLInputElement>) => (
  <input
    {...props}
    className="w-full rounded-xl border border-gray-200 bg-white px-3 py-2 text-sm text-gray-900 outline-none focus:ring-2 focus:ring-offset-0"
    style={{ boxShadow: "none" }}
  />
);

const Select = (props: React.SelectHTMLAttributes<HTMLSelectElement>) => (
  <select
    {...props}
    className="w-full rounded-xl border border-gray-200 bg-white px-3 py-2 text-sm text-gray-900 outline-none focus:ring-2 focus:ring-offset-0"
  />
);

const Toggle = ({ checked, onChange }: { checked: boolean; onChange: (v: boolean) => void }) => (
  <button
    type="button"
    onClick={() => onChange(!checked)}
    className="h-6 w-11 rounded-full transition-colors bg-gray-200 relative"
    style={{ backgroundColor: checked ? PRIMARY : undefined }}
    aria-pressed={checked}
  >
    <span
      className={`absolute top-0.5 h-5 w-5 rounded-full bg-white shadow transition-transform ${
        checked ? "translate-x-6" : "translate-x-1"
      }`}
    />
  </button>
);

const PrimaryBtn = (p: React.ButtonHTMLAttributes<HTMLButtonElement>) => (
  <button
    {...p}
    className="inline-flex items-center gap-2 rounded-xl px-4 py-2 text-sm font-semibold text-white"
    style={{ background: PRIMARY }}
  />
);

const GhostBtn = (p: React.ButtonHTMLAttributes<HTMLButtonElement>) => (
  <button
    {...p}
    className="inline-flex items-center gap-2 rounded-xl px-4 py-2 text-sm font-semibold border border-gray-200 text-gray-800 bg-white hover:bg-gray-50"
  />
);

/* Page */
export default function SettingsPage() {
  // Account
  const [name, setName] = useState("Ostora Youssef");
  const [email, setEmail] = useState("<EMAIL>");
  const [phone, setPhone] = useState("+212 6 00 00 00 00");
  const [lang, setLang] = useState("en");
  const [tz, setTz] = useState("Africa/Casablanca");

  // Org / Farm
  const [farm, setFarm] = useState("Farmti Demo Farm");
  const [address, setAddress] = useState("Settat, Morocco");

  // Notifications
  const [nEmail, setNEmail] = useState(true);
  const [nSMS, setNSMS] = useState(false);
  const [nCritical, setNCritical] = useState(true);
  const [nWeekly, setNWeekly] = useState(true);

  // Security
  const [twoFA, setTwoFA] = useState(false);

  // Billing
  const [plan, setPlan] = useState("pro");
  const [cardLast4, setCardLast4] = useState("4242");

  const saveAll = () => {
    const payload = {
      account: { name, email, phone, lang, tz },
      farm: { farm, address },
      notifications: { nEmail, nSMS, nCritical, nWeekly },
      security: { twoFA },
      billing: { plan },
    };
    console.info("SETTINGS_SAVE", payload);
    alert("Settings saved.");
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Settings</h2>
          <p className="text-sm text-gray-600">Manage account, farm, security, notifications, and billing.</p>
        </div>
        <PrimaryBtn onClick={saveAll}>
          <Save size={16} />
          Save changes
        </PrimaryBtn>
      </div>

      {/* Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        {/* Col 1 */}
        <div className="space-y-6">
          <Section title="Account" icon={<User size={16} className="text-gray-700" />}>
            <div className="grid gap-4">
              <div className="grid gap-1.5">
                <Label htmlFor="name">Full name</Label>
                <Input id="name" value={name} onChange={(e) => setName(e.target.value)} />
              </div>
              <div className="grid gap-1.5">
                <Label htmlFor="email"><span className="inline-flex items-center gap-1"><Mail size={14}/> Email</span></Label>
                <Input id="email" type="email" value={email} onChange={(e) => setEmail(e.target.value)} />
              </div>
              <div className="grid gap-1.5">
                <Label htmlFor="phone"><span className="inline-flex items-center gap-1"><Phone size={14}/> Phone</span></Label>
                <Input id="phone" value={phone} onChange={(e) => setPhone(e.target.value)} />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-1.5">
                  <Label htmlFor="lang"><span className="inline-flex items-center gap-1"><Globe size={14}/> Language</span></Label>
                  <Select id="lang" value={lang} onChange={(e) => setLang(e.target.value)}>
                    <option value="en">English</option>
                    <option value="fr">Français</option>
                    <option value="ar">العربية</option>
                  </Select>
                </div>
                <div className="grid gap-1.5">
                  <Label htmlFor="tz">Time zone</Label>
                  <Select id="tz" value={tz} onChange={(e) => setTz(e.target.value)}>
                    <option value="Africa/Casablanca">Africa/Casablanca</option>
                    <option value="UTC">UTC</option>
                    <option value="Europe/Paris">Europe/Paris</option>
                  </Select>
                </div>
              </div>
            </div>
          </Section>

          <Section title="Farm / Organization" icon={<Building2 size={16} className="text-gray-700" />}>
            <div className="grid gap-4">
              <div className="grid gap-1.5">
                <Label htmlFor="farm">Farm name</Label>
                <Input id="farm" value={farm} onChange={(e) => setFarm(e.target.value)} />
              </div>
              <div className="grid gap-1.5">
                <Label htmlFor="address">Address</Label>
                <Input id="address" value={address} onChange={(e) => setAddress(e.target.value)} />
              </div>
            </div>
          </Section>
        </div>

        {/* Col 2 */}
        <div className="space-y-6">
          <Section title="Notifications" icon={<Bell size={16} className="text-gray-700" />}>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-800">Email alerts</p>
                  <p className="text-xs text-gray-500">Send alerts and weekly summaries to your email.</p>
                </div>
                <Toggle checked={nEmail} onChange={setNEmail} />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-800">SMS alerts</p>
                  <p className="text-xs text-gray-500">Critical alerts via SMS for urgent issues.</p>
                </div>
                <Toggle checked={nSMS} onChange={setNSMS} />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-800">Critical alerts</p>
                  <p className="text-xs text-gray-500">Notify on leaks, pump faults, or offline gateways.</p>
                </div>
                <Toggle checked={nCritical} onChange={setNCritical} />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-800">Weekly report</p>
                  <p className="text-xs text-gray-500">Usage, efficiency, and threshold breaches.</p>
                </div>
                <Toggle checked={nWeekly} onChange={setNWeekly} />
              </div>
            </div>
          </Section>

          <Section title="Security" icon={<Shield size={16} className="text-gray-700" />}>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-1.5">
                  <Label htmlFor="pwd1"><span className="inline-flex items-center gap-1"><Lock size={14}/> New password</span></Label>
                  <Input id="pwd1" type="password" placeholder="••••••••" />
                </div>
                <div className="grid gap-1.5">
                  <Label htmlFor="pwd2">Confirm password</Label>
                  <Input id="pwd2" type="password" placeholder="••••••••" />
                </div>
              </div>

              <div className="flex items-center justify-between border-t pt-4">
                <div>
                  <p className="text-sm font-medium text-gray-800">Two-factor authentication</p>
                  <p className="text-xs text-gray-500">Add an extra layer of security to your account.</p>
                </div>
                <Toggle checked={twoFA} onChange={setTwoFA} />
              </div>

              {twoFA && (
                <div className="mt-3 rounded-xl border border-gray-200 p-4">
                  <div className="flex items-center gap-2 text-sm font-semibold text-gray-800 mb-2">
                    <QrCode size={16} />
                    Authenticator setup
                  </div>
                  <p className="text-sm text-gray-600">
                    Scan the QR in your authenticator app, then enter the 6-digit code to confirm.
                  </p>
                  <div className="mt-3 grid grid-cols-2 gap-3">
                    <Input placeholder="123 456" />
                    <PrimaryBtn><KeyRound size={16}/> Verify</PrimaryBtn>
                  </div>
                </div>
              )}
            </div>
          </Section>
        </div>

        {/* Col 3 */}
        <div className="space-y-6">
          <Section title="Billing" icon={<CreditCard size={16} className="text-gray-700" />}>
            <div className="grid gap-4">
              <div className="grid gap-1.5">
                <Label>Plan</Label>
                <Select value={plan} onChange={(e) => setPlan(e.target.value)}>
                  <option value="free">Free — 1 farm, 3 plots</option>
                  <option value="pro">Pro — 5 farms, 50 plots</option>
                  <option value="enterprise">Enterprise — custom</option>
                </Select>
              </div>
              <div className="grid gap-1.5">
                <Label>Payment method</Label>
                <div className="flex items-center justify-between rounded-xl border border-gray-200 px-3 py-2">
                  <div className="text-sm text-gray-700">Visa •••• {cardLast4}</div>
                  <GhostBtn onClick={() => setCardLast4(prompt("Last 4 digits:", cardLast4 || "") || cardLast4)}>
                    Update card
                  </GhostBtn>
                </div>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Next invoice</span>
                <span className="font-medium text-gray-900">Oct 1, 2025</span>
              </div>
            </div>
          </Section>

          <Section title="Integrations" icon={<Link2 size={16} className="text-gray-700" />}>
            <div className="grid gap-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-800">Webhook for events</span>
                <GhostBtn>Configure</GhostBtn>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-800">API access</span>
                <GhostBtn>Manage keys</GhostBtn>
              </div>
            </div>
          </Section>

          <Section title="Data & Privacy" icon={<Download size={16} className="text-gray-700" />}>
            <div className="grid gap-3">
              <GhostBtn onClick={() => alert("Export requested.")}>
                <Download size={16} />
                Export my data
              </GhostBtn>
              <button
                className="inline-flex items-center gap-2 rounded-xl px-4 py-2 text-sm font-semibold text-white"
                style={{ background: "#ef4444" }}
                onClick={() => confirm("Permanently delete account and data?") && alert("Deletion scheduled.")}
              >
                <Trash2 size={16} />
                Delete account
              </button>
            </div>
          </Section>
        </div>
      </div>

      {/* Footer bar */}
      <div className="sticky bottom-0 left-0 right-0 border-t border-gray-200 bg-white/80 backdrop-blur supports-[backdrop-filter]:bg-white/60">
        <div className="max-w-screen-2xl mx-auto px-6 py-3 flex items-center justify-end gap-3">
          <GhostBtn onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}>Back to top</GhostBtn>
          <PrimaryBtn onClick={saveAll}><Save size={16}/> Save changes</PrimaryBtn>
        </div>
      </div>
    </div>
  );
}
