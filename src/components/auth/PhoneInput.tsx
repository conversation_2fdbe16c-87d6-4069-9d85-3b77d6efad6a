import { useState } from 'react';
import { ChevronDown, Phone } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface Country {
  code: string;
  name: string;
  flag: string;
  dialCode: string;
}

const countries: Country[] = [
  { code: 'MA', name: 'Morocco', flag: '🇲🇦', dialCode: '+212' },
  { code: 'US', name: 'United States', flag: '🇺🇸', dialCode: '+1' },
  { code: 'GB', name: 'United Kingdom', flag: '🇬🇧', dialCode: '+44' },
  { code: 'FR', name: 'France', flag: '🇫🇷', dialCode: '+33' },
  { code: 'DE', name: 'Germany', flag: '🇩🇪', dialCode: '+49' },
  { code: 'ES', name: 'Spain', flag: '🇪🇸', dialCode: '+34' },
  { code: 'IT', name: 'Italy', flag: '🇮🇹', dialCode: '+39' },
  { code: 'CA', name: 'Canada', flag: '🇨🇦', dialCode: '+1' },
  { code: 'AU', name: 'Australia', flag: '🇦🇺', dialCode: '+61' },
  { code: 'IN', name: 'India', flag: '🇮🇳', dialCode: '+91' },
  { code: 'JP', name: 'Japan', flag: '🇯🇵', dialCode: '+81' },
  { code: 'CN', name: 'China', flag: '🇨🇳', dialCode: '+86' },
];

interface PhoneInputProps {
  value: string;
  onChange: (value: string) => void;
  error?: string;
}

export const PhoneInput = ({ value, onChange, error }: PhoneInputProps) => {
  const [selectedCountry, setSelectedCountry] = useState<Country>(countries[0]); // Default to Morocco
  
  // Extract the phone number without country code
  const phoneNumber = value.startsWith(selectedCountry.dialCode) 
    ? value.slice(selectedCountry.dialCode.length)
    : value;

  const handleCountryChange = (country: Country) => {
    setSelectedCountry(country);
    // Update the full phone number with new country code
    const cleanNumber = phoneNumber.replace(/\D/g, '');
    onChange(country.dialCode + cleanNumber);
  };

  const handlePhoneChange = (newPhoneNumber: string) => {
    // Only allow digits, spaces, and common phone number characters
    const cleaned = newPhoneNumber.replace(/[^\d\s\-\(\)]/g, '');
    // Format the full phone number
    const fullNumber = selectedCountry.dialCode + cleaned.replace(/\D/g, '');
    onChange(fullNumber);
  };

  const formatPhoneForDisplay = (phone: string) => {
    // Remove non-digits for formatting
    const digits = phone.replace(/\D/g, '');
    
    // Simple formatting for display (can be enhanced based on country)
    if (digits.length <= 3) return digits;
    if (digits.length <= 6) return `${digits.slice(0, 3)} ${digits.slice(3)}`;
    if (digits.length <= 10) return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6)}`;
    return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6, 10)}`;
  };

  return (
    <div className="flex">
      {/* Country Selector */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className={`flex items-center gap-2 rounded-r-none border-r-0 px-3 ${error ? 'border-destructive' : ''}`}
            type="button"
          >
            <span className="text-lg">{selectedCountry.flag}</span>
            <span className="text-sm font-medium">{selectedCountry.dialCode}</span>
            <ChevronDown className="h-4 w-4 text-muted-foreground" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-64 max-h-64 overflow-y-auto bg-popover border border-border">
          {countries.map((country) => (
            <DropdownMenuItem
              key={country.code}
              onClick={() => handleCountryChange(country)}
              className="bg-gray-100 flex items-center gap-3 cursor-pointer hover:bg-accent"
            >
              <span className="text-lg">{country.flag}</span>
              <span className="flex-1">{country.name}</span>
              <span className="text-sm text-muted-foreground">{country.dialCode}</span>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Phone Number Input */}
      <div className="relative flex-1">
        <Phone className="absolute left-3 top-3 h-5 w-5 text-muted-foreground" />
        <Input
          id="signup-phone"
          type="tel"
          placeholder="Enter phone number"
          value={formatPhoneForDisplay(phoneNumber)}
          onChange={(e) => handlePhoneChange(e.target.value)}
          className={`pl-10 rounded-l-none form-input ${error ? 'error' : ''}`}
          inputMode="tel"
          autoComplete="tel"
          aria-describedby={error ? 'phone-error' : undefined}
        />
      </div>
    </div>
  );
};