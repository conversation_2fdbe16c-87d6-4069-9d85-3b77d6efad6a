'use client';
import { useEffect } from 'react';
import { useAppDispatch } from '@/lib/hooks';
import { setCredentials } from '@/lib/store/authSlice';

export default function ClientHydrator() {
  const dispatch = useAppDispatch();

  useEffect(() => {
    try {
      const storedUser = localStorage.getItem('data');
      const storedAccess = localStorage.getItem('access');
      if (storedAccess) {
        // Set token so prepareHeaders & queries use it
        dispatch(setCredentials({ access: storedAccess, user: storedUser ? JSON.parse(storedUser) : undefined }));
      }
    } catch {}
  }, [dispatch]);

  return null;
}
