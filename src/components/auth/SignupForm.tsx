import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Phone } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
// import { Label } from '@/components/ui/label';
import { Checkbox } from "@/components/ui/checkbox";
import { PhoneInput } from "./PhoneInput";
import {useRouter} from "next/navigation";

interface SignupFormData {
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  password: string;
  confirmPassword: string;
  agreeToTerms: boolean;
}

interface SignupErrors {
  firstName?: string;
  lastName?: string;
  phone?: string;
  email?: string;
  password?: string;
  confirmPassword?: string;
  agreeToTerms?: string;
  general?: string;
}

interface PasswordStrength {
  score: number;
  label: string;
  color: string;
  checks: {
    length: boolean;
    upperLower: boolean;
    number: boolean;
    symbol: boolean;
  };
}

interface SignupFormProps {
  onToggleToLogin?: () => void;
}

const getPasswordStrength = (password: string): PasswordStrength => {
  const checks = {
    length: password.length >= 8,
    upperLower: /[a-z]/.test(password) && /[A-Z]/.test(password),
    number: /\d/.test(password),
    symbol: /[!@#$%^&*(),.?":{}|<>]/.test(password),
  };

  const score = Object.values(checks).filter(Boolean).length;

  const strengthMap = {
    0: { label: "Weak", color: "weak" },
    1: { label: "Weak", color: "weak" },
    2: { label: "Fair", color: "fair" },
    3: { label: "Good", color: "good" },
    4: { label: "Strong", color: "strong" },
  };

  return {
    score,
    label: strengthMap[score as keyof typeof strengthMap].label,
    color: strengthMap[score as keyof typeof strengthMap].color,
    checks,
  };
};

export const SignupForm = ({ onToggleToLogin }: SignupFormProps) => {
  const [formData, setFormData] = useState<SignupFormData>({
    firstName: "",
    lastName: "",
    phone: "",
    email: "",
    password: "",
    confirmPassword: "",
    agreeToTerms: false,
  });
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<SignupErrors>>({});

  const passwordStrength = getPasswordStrength(formData.password);

  const validateForm = (): boolean => {
    const newErrors: any = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = "First name is required";
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = "Last name is required";
    }

    if (!formData.phone.trim()) {
      newErrors.phone = "Phone number is required";
    } else if (formData.phone.length < 10) {
      newErrors.phone = "Please enter a valid phone number";
    }
    interface SignupFormData {
      firstName: string;
      lastName: string;
      phone: string;
      email: string;
      password: string;
      confirmPassword: string;
      // Remove agreeToTerms
    }

    interface SignupErrors {
      firstName?: string;
      lastName?: string;
      phone?: string;
      email?: string;
      password?: string;
      confirmPassword?: string;
      // Remove agreeToTerms
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email";
    }

    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (passwordStrength.score < 3) {
      newErrors.password = "Password is too weak";
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "Please confirm your password";
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = "You must agree to the terms and conditions";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const data = {
        first_name: formData.firstName,
        last_name: formData.lastName,
        phone_number: formData.phone,
        email: formData.email,
        password: formData.password,
      }
      const response = await fetch('http://127.0.0.1:8080/v1/users/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        router.push(`/activate-message?email=${encodeURIComponent(data.email)}`);
        console.log('Signup successful');
      } else {
        const errorData = await response.json();
        console.error('Signup error:', errorData);
        setErrors((prev) => ({
          ...prev,
          email: errorData.email ? errorData.email[0] : undefined,
          phone: errorData.phone_number ? errorData.phone_number[0] : undefined,
          // Add other specific error handling as needed
        }));
      }
    } catch (error) {
      console.error("Signup failed:", error);
      setErrors((prev) => ({
        ...prev,
        general: 'Registration failed. Please try again.',
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (
    field: keyof SignupFormData,
    value: string | boolean
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear the specific error when user makes changes
    setErrors((prev) => ({ ...prev, [field]: undefined }));
  };

  return (
    <div className="w-full max-w-sm mx-auto">
      <form onSubmit={handleSubmit} className="space-y-4" noValidate>
        <h2 className="text-xl font-bold text-center mb-6">Create Account</h2>

        {/* First Name and Last Name Row */}
        <div className="grid grid-cols-2 gap-4">
          <div className="relative">
            <User className="absolute left-3 top-3 h-5 w-5 text-muted-foreground" />
            <Input
              id="firstName"
              type="text"
              placeholder="First Name"
              value={formData.firstName}
              onChange={(e) => handleChange("firstName", e.target.value)}
              className={`pl-10 form-input ${errors.firstName ? "error" : ""}`}
              aria-label="First Name"
              aria-describedby={
                errors.firstName ? "firstName-error" : undefined
              }
              autoComplete="given-name"
            />
            {errors.firstName && (
              <p
                id="firstName-error"
                className="text-sm text-destructive mt-1"
                role="alert"
              >
                {errors.firstName}
              </p>
            )}
          </div>

          <div className="relative">
            <User className="absolute left-3 top-3 h-5 w-5 text-muted-foreground" />
            <Input
              id="lastName"
              type="text"
              placeholder="Last Name"
              value={formData.lastName}
              onChange={(e) => handleChange("lastName", e.target.value)}
              className={`pl-10 form-input ${errors.lastName ? "error" : ""}`}
              aria-label="Last Name"
              aria-describedby={errors.lastName ? "lastName-error" : undefined}
              autoComplete="family-name"
            />
            {errors.lastName && (
              <p
                id="lastName-error"
                className="text-sm text-destructive mt-1"
                role="alert"
              >
                {errors.lastName}
              </p>
            )}
          </div>
        </div>

        {/* Phone Field */}
        <div className="relative ">
          <PhoneInput
            value={formData.phone}
            onChange={(value: string) => handleChange("phone", value)}
            error={errors.phone}
          />
          {errors.phone && (
            <p className="text-sm text-destructive mt-1" role="alert">
              {errors.phone}
            </p>
          )}
        </div>

        {/* Email Field */}
        <div className="relative">
          <Mail className="absolute left-3 top-3 h-5 w-5 text-muted-foreground" />
          <Input
            id="signup-email"
            type="email"
            placeholder="Email Address"
            value={formData.email}
            onChange={(e) => handleChange("email", e.target.value)}
            className={`pl-10 form-input ${errors.email ? "error" : ""}`}
            aria-label="Email Address"
            aria-describedby={errors.email ? "email-error" : undefined}
            autoComplete="email"
          />
          {errors.email && (
            <p
              id="email-error"
              className="text-sm text-destructive mt-1"
              role="alert"
            >
              {errors.email}
            </p>
          )}
        </div>

        {/* Password Field */}
        <div className="relative">
          <Lock className="absolute left-3 top-3 h-5 w-5 text-muted-foreground" />
          <Input
            id="signup-password"
            type={showPassword ? "text" : "password"}
            placeholder="Password"
            value={formData.password}
            onChange={(e) => handleChange("password", e.target.value)}
            className={`pl-10 pr-10 form-input ${
              errors.password ? "error" : ""
            }`}
            aria-label="Password"
            aria-describedby={
              errors.password ? "password-error" : "password-strength"
            }
            autoComplete="new-password"
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-3 text-muted-foreground hover:text-foreground transition-colors"
            aria-label={showPassword ? "Hide password" : "Show password"}
          >
            {showPassword ? (
              <EyeOff className="h-5 w-5" />
            ) : (
              <Eye className="h-5 w-5" />
            )}
          </button>

          {/* Password Strength Meter */}
          <div className="strength-meter">
            <div
              className={`strength-bar strength-${passwordStrength.label.toLowerCase()}`}
              style={{
                width: `${passwordStrength.score * 25}%`,
                transform: `scaleX(${passwordStrength.score * 0.25})`,
                transformOrigin: "left",
                transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
              }}
            />
          </div>

          {errors.password && (
            <p
              id="password-error"
              className="text-sm text-destructive mt-1"
              role="alert"
            >
              {errors.password}
            </p>
          )}
        </div>

        {/* Confirm Password Field */}
        <div className="relative">
          <Lock className="absolute left-3 top-3 h-5 w-5 text-muted-foreground" />
          <Input
            id="confirm-password"
            type={showConfirmPassword ? "text" : "password"}
            placeholder="Confirm Password"
            value={formData.confirmPassword}
            onChange={(e) => handleChange("confirmPassword", e.target.value)}
            className={`pl-10 pr-10 form-input ${
              errors.confirmPassword ? "error" : ""
            }`}
            aria-label="Confirm Password"
            aria-describedby={
              errors.confirmPassword ? "confirm-password-error" : undefined
            }
            autoComplete="new-password"
          />
          <button
            type="button"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            className="absolute right-3 top-3 text-muted-foreground hover:text-foreground transition-colors"
            aria-label={showConfirmPassword ? "Hide password" : "Show password"}
          >
            {showConfirmPassword ? (
              <EyeOff className="h-5 w-5" />
            ) : (
              <Eye className="h-5 w-5" />
            )}
          </button>
          {errors.confirmPassword && (
            <p
              id="confirm-password-error"
              className="text-sm text-destructive mt-1"
              role="alert"
            >
              {errors.confirmPassword}
            </p>
          )}
        </div>

        {/* Terms and Conditions */}
        <div className="flex items-start space-x-2">
          <Checkbox
            id="agree-terms"
            checked={formData.agreeToTerms}
            onCheckedChange={(checked) =>
              handleChange("agreeToTerms", !!checked)
            }
            className="mt-1"
          />
          <div className="grid gap-1.5 leading-none">
            <label
              htmlFor="agree-terms"
              className="text-sm font-normal cursor-pointer leading-relaxed"
            >
              I agree to the{" "}
              <a href="#" className="text-primary hover:underline">
                Terms of Service
              </a>{" "}
              and{" "}
              <a href="#" className="text-primary hover:underline">
                Privacy Policy
              </a>
            </label>
            {errors.agreeToTerms && (
              <p className="text-sm text-destructive" role="alert">
                {errors.agreeToTerms}
              </p>
            )}
          </div>
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          disabled={isLoading}
          className="w-full btn-primary py-3 text-base font-medium"
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <Loader2 className="h-5 w-5 animate-spin" />
              Creating account...
            </div>
          ) : (
            "Create Account"
          )}
        </Button>

        {/* General Error Message */}
        {errors.general && (
          <p className="text-sm text-destructive text-center mt-2" role="alert">
            {errors.general}
          </p>
        )}
      </form>
    </div>
  );
};
