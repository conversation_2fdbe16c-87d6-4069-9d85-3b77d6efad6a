'use client';

import React from 'react';
import { 
  LayoutDashboard, 
  Map, 
  Droplets, 
  SlidersHorizontal, 
  FileText, 
  Settings,
  User,
  X,
  ChevronRight
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useAppSelector } from '@/lib/hooks';

interface SidebarItem {
  icon: React.ReactNode;
  label: string;
  id: string;
  badge?: number;
}

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  activeSection: string;
  onSectionChange: (section: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ 
  isOpen, 
  onClose, 
  activeSection, 
  onSectionChange 
}) => {
  const router = useRouter();
  const { user, isLoading } = useAppSelector((s) => s.auth);

  const sidebarItems: SidebarItem[] = [
    { icon: <LayoutDashboard size={20} />, label: 'Dashboard', id: 'dashboard' },
    { icon: <Map size={20} />, label: 'Map', id: 'map' },
    { icon: <Droplets size={20} />, label: 'Water Plan', id: 'water-plan' },
    { icon: <Droplets size={20} />, label: 'Water Inputs', id: 'water-inputs' },
    { icon: <SlidersHorizontal size={20} />, label: 'Configuration', id: 'configuration' },
    { icon: <FileText size={20} />, label: 'Reports', id: 'reports' },
    { icon: <Settings size={20} />, label: 'Settings', id: 'settings' }
  ];

  const handleProfileClick = () => {
    router.push('/dashboard/profile');
    // Close sidebar on mobile after navigation
    if (window.innerWidth < 1024) {
      onClose();
    }
  };

  let displayName = user?.first_name ?? user?.name ?? user?.email ?? 'User';
  const userRole = user?.role ?? 'Farm Manager';
  displayName = displayName.split('@')[0];


  return (
    <>
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      <div className={`fixed inset-y-0 left-0 z-50 w-72 bg-white shadow-2xl transform transition-transform duration-300 ease-in-out ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      } lg:translate-x-0`}>

        <div className="flex items-center justify-between h-20 px-6 border-b border-gray-100">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-[#21c45d] to-[#21c45d]/90 rounded-xl flex items-center justify-center shadow-lg">
              <Droplets className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">Farmti</h1>
              <p className="text-xs text-gray-500">Smart Farm Management</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <X size={20} className="text-gray-600" />
          </button>
        </div>

        <nav className="mt-8 px-4">
          <div className="space-y-2">
            {sidebarItems.map((item) => (
              <button
                key={item.id}
                onClick={() => onSectionChange(item.id)}
                className={`cursor-pointer w-full flex items-center justify-between px-4 py-3 rounded-xl transition-all duration-200 group ${
                  activeSection === item.id 
                    ? 'bg-gradient-to-r from-[#21c45d] to-[#21c45d]/90 text-white shadow-lg shadow-[#21c45d]/25' 
                    : 'text-gray-700 hover:bg-[#21c45d] hover:text-white'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div className={`transition-transform duration-200 ${
                    activeSection === item.id ? 'scale-110' : 'group-hover:scale-105'
                  }`}>
                    {item.icon}
                  </div>
                  <span className="font-medium">{item.label}</span>
                </div>
                {item.badge && (
                  <span className={`px-2 py-1 text-xs font-bold rounded-full ${
                    activeSection === item.id ? 'bg-white/20 text-white' : 'bg-red-100 text-red-600'
                  }`}>
                    {item.badge}
                  </span>
                )}
              </button>
            ))}
          </div>
        </nav>

        {/* Profile Section */}
        <div className="absolute bottom-6 left-4 right-4">
          <button
            onClick={handleProfileClick}
            className="cursor-pointer ent-to-r from-gray-50 to-[#21c45d]/10 rounded-2xl p-4 border border-gray-100 hover:from-[#21c45d]/10 hover:to-[#21c45d]/20 hover:border-[#21c45d]/30 transition-all duration-200 group"
          >
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-[#21c45d] to-[#21c45d]/90 rounded-xl flex items-center justify-center shadow-md group-hover:scale-105 transition-transform duration-200">
                <User className="w-6 h-6 text-white" />
              </div>
              <div className="flex-1 text-left">
                {isLoading ? (
                  <div className="space-y-1">
                    <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-3 bg-gray-200 rounded w-2/3 animate-pulse"></div>
                  </div>
                ) : (
                  <>
                    <p className="font-semibold text-gray-900 group-hover:text-[#21c45d] transition-colors duration-200">
                      {displayName}
                    </p>
                    <p className="text-sm text-gray-600">{userRole}</p>
                  </>
                )}
              </div>
              <ChevronRight size={16} className="text-gray-400 group-hover:text-[#21c45d] group-hover:translate-x-1 transition-all duration-200" />
            </div>
          </button>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
