body {
  margin: 0;
  padding: 0;
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu",
    "Cantarell", sans-serif;
  background: #f8fafc;
  color: #1e293b;
}

.container {
  display: flex;
  height: 100%;
  width: 100%;
}

.map-overlay {
  position: absolute;
  margin: 0.2rem;
  /* background-color: rgb(35 55 75 / 90%); */
  display: flex;
  flex-direction: column;
  z-index: 1;
  /* justify-content: space-around; */
  /* top: 0;
  left: 0; */
  /* border: 1px solid red; */
  width: 14rem;
  height: 8rem;
  pointer-events: none; /* Allow interactions to pass through */
}

.map-overlay .coordinates-display {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  pointer-events: auto; /* Enable interactions for the display container */
}

.map-overlay .coordinates-display p {
  margin: 0.1rem;
  font-size: 0.75rem;
  color: #ffffff;
}

.map-overlay .reset-button {
  /* position: absolute; */
  padding: 4px 10px;
  cursor: pointer;
  max-width: fit-content;
  justify-self: center;
  align-self: center;
  margin: 0.1rem;
  z-index: 1;
  color: #ffffff;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  pointer-events: auto; /* Enable interactions for the button */
}

.map-section {
  width: 100%;
  height: 100%;
}

/* Modern Sidebar Styles */
.modern-sidebar {
  width: 320px;
  background: #ffffff;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.modern-sidebar.collapsed {
  width: 64px;
}

/* Header */
.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #3b82f6;
  font-weight: 600;
  font-size: 16px;
}

.logo svg {
  flex-shrink: 0;
}

.collapse-btn {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 6px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.collapse-btn:hover {
  background: #e2e8f0;
  color: #3b82f6;
}

/* Navigation */
.sidebar-nav {
  padding: 16px 8px;
  border-bottom: 1px solid #e2e8f0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  margin-bottom: 4px;
  background: none;
  border: none;
  border-radius: 8px;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.nav-item:hover {
  background: #f1f5f9;
  color: #3b82f6;
}

.nav-item.active {
  background: #dbeafe;
  color: #3b82f6;
}

.nav-item svg {
  flex-shrink: 0;
}

/* Content Sections */
.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.content-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #475569;
}

.zone-count {
  background: #3b82f6;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

/* Form Elements */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.color-input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.color-input {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  border: 2px solid #e2e8f0;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.color-input:hover {
  border-color: #3b82f6;
}

.color-value {
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace;
  font-size: 12px;
  color: #64748b;
  background: #f1f5f9;
  padding: 4px 8px;
  border-radius: 4px;
}

.text-input {
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.text-input:focus {
  outline: none;
  border-color: #3b82f6;
}

/* Buttons */
.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn.primary {
  background: #3b82f6;
  color: white;
}

.action-btn.primary:hover {
  background: #2563eb;
}

.action-btn.secondary {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.action-btn.secondary:hover {
  background: #e2e8f0;
}

.action-btn.danger {
  background: #ef4444;
  color: white;
}

.action-btn.danger:hover {
  background: #dc2626;
}

.icon-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.icon-btn:hover {
  background: #e2e8f0;
  color: #3b82f6;
}

.icon-btn.danger {
  color: #ef4444;
}

.icon-btn.danger:hover {
  background: #fee2e2;
  color: #dc2626;
}

/* Help Text */
.help-text {
  font-size: 12px;
  color: #64748b;
  line-height: 1.5;
  background: #f8fafc;
  padding: 12px;
  border-radius: 6px;
  border-left: 3px solid #3b82f6;
}

/* Zone Cards */
.zones-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.zone-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.zone-card:hover {
  background: #f1f5f9;
  border-color: #3b82f6;
}

.zone-color {
  width: 12px;
  height: 12px;
  border-radius: 3px;
  flex-shrink: 0;
}

.zone-details {
  flex: 1;
}

.zone-name {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 2px;
}

.zone-area {
  font-size: 12px;
  color: #64748b;
}

.zone-actions {
  display: flex;
  gap: 4px;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 32px 16px;
  color: #64748b;
}

.empty-state svg {
  margin-bottom: 16px;
}

.empty-state p {
  margin: 0 0 4px 0;
  font-weight: 500;
}

.empty-state span {
  font-size: 12px;
}

/* Tool Group */
.tool-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Footer */
.sidebar-footer {
  border-top: 1px solid #e2e8f0;
  padding: 16px;
}

.coordinates-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.coordinates-display {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 12px;
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace;
  font-size: 11px;
  color: #64748b;
  max-height: 100px;
  overflow-y: auto;
  line-height: 1.4;
}

/* Map Container */
#map-container {
  /* flex: 1;
  position: relative; */
  width: 100%;
  height: 100%;
}

/* #map-container {
  width: 100%;
  height: 100%;
} */

.sidebar {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 12px;
  color: #64748b;
  border: 1px solid #e2e8f0;
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace;
}

.reset-button:hover {
  background: #f9fafb;
  border-color: #000;
  color: #000;
}

/* Scrollbar Styling */
.zones-container::-webkit-scrollbar,
.coordinates-display::-webkit-scrollbar {
  width: 6px;
}

.zones-container::-webkit-scrollbar-track,
.coordinates-display::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.zones-container::-webkit-scrollbar-thumb,
.coordinates-display::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.zones-container::-webkit-scrollbar-thumb:hover,
.coordinates-display::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Mapbox Popup Customization */
.mapboxgl-popup-content {
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}
