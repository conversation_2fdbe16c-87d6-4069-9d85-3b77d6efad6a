.vertical-toolbar {
  position: absolute;
  top: 50%;
  left: 1rem;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(200, 200, 200, 0.4);
  border-radius: 1rem;
  padding: 0.75rem;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.vertical-toolbar .menu-toggle {
  /* position: relative; */
  align-self: center;
  padding: 0.3rem;
  width: 2rem;
  height: 2rem;
  border: none;
  outline: none;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
  overflow: hidden;
  /* margin-bottom: 12px; */
}

.vertical-toolbar .menu-toggle:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.vertical-toolbar .menu-toggle:active {
  transform: translateY(0px) scale(0.95);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Hamburger Animation */
.hamburger {
  width: 20px;
  height: 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

.hamburger-line {
  width: 100%;
  height: 2px;
  background: #374151;
  border-radius: 2px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

/* Collapsed state - hamburger transforms to X */
.menu-toggle.collapsed .hamburger-line:nth-child(1) {
  transform: translateY(7px) rotate(45deg);
  background: #ef4444;
}

.menu-toggle.collapsed .hamburger-line:nth-child(2) {
  opacity: 0;
  transform: scaleX(0);
}

.menu-toggle.collapsed .hamburger-line:nth-child(3) {
  transform: translateY(-7px) rotate(-45deg);
  background: #ef4444;
}

/* Ripple Effect */
.ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.3);
  transform: translate(-50%, -50%);
  transition: all 0.6s ease-out;
  pointer-events: none;
}

.menu-toggle:active .ripple {
  width: 60px;
  height: 60px;
  background: rgba(59, 130, 246, 0.1);
}

/* Enhanced hover states */
.menu-toggle:hover .hamburger-line {
  background: #1f2937;
}

.menu-toggle.collapsed:hover .hamburger-line {
  background: #dc2626;
}

/* Micro-interactions */
.menu-toggle:hover .hamburger {
  transform: scale(1.1);
}

.menu-toggle.collapsed:hover .hamburger {
  transform: scale(1.1) rotate(180deg);
}

/* Smooth state transitions */
.menu-toggle.collapsed {
  background: rgba(254, 242, 242, 0.9);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.tool-button {
  width: 48px;
  height: 48px;
  border: none;
  outline: none;
  background: transparent;
  color: #555;
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.25s ease;
}

.tool-button:hover {
  background: rgba(230, 230, 230, 0.9);
  color: #2563eb; /* blue hover color */
}

.tool-button.active {
  background: linear-gradient(135deg, #2563eb, #4f46e5);
  color: white;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
  transform: scale(1.05);
}

.tool-icon {
  width: 22px;
  height: 22px;
  pointer-events: none;
}

/* Tool Container for popups */
.tool-container {
  position: relative;
}

/* Danger button style */
.tool-button.danger {
  border: 2px solid #ef4444;
}

.tool-button.danger:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.tool-button.danger.active {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

/* Tool Popups */
.tool-popup {
  position: absolute;

  left: calc(100% + 16px);
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(200, 200, 200, 0.4);
  border-radius: 0.75rem;
  padding: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  min-width: 150px;
}

/* Color Picker Popup */
.color-popup {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.color-input {
  width: 48px;
  height: 48px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  outline: none;
}

.color-value {
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace;
  font-size: 11px;
  color: #64748b;
  background: rgba(241, 245, 249, 0.8);
  padding: 4px 8px;
  border-radius: 4px;
}

/* Name Input Popup */
.name-popup {
  display: flex;
  align-items: center;
}

.name-input {
  padding: 8px 12px;
  border: 2px solid rgba(226, 232, 240, 0.8);
  border-radius: 6px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.9);
  outline: none;
  transition: border-color 0.2s ease;
}

.name-input:focus {
  border-color: #3b82f6;
}

/* Zone Counter */
.zone-counter {
  margin-top: 8px;
  padding: 8px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 0.75rem;
  text-align: center;
  font-size: 12px;
}

.zone-count {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #3b82f6;
  line-height: 1;
}

.zone-label {
  color: #64748b;
  font-size: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Zone Popup Styles */
.zone-popup {
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu",
    "Cantarell", sans-serif;
  min-width: 250px;
  color: #000;
}

.zone-popup-header {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.zone-popup-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.zone-popup-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.popup-field {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.popup-field label {
  font-size: 12px;
  font-weight: 500;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.popup-input {
  padding: 8px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.popup-input:focus {
  border-color: #3b82f6;
}

.color-picker-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.popup-color-input {
  width: 32px;
  height: 32px;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  cursor: pointer;
  outline: none;
}

.color-display {
  width: 16px;
  height: 16px;
  border-radius: 3px;
  border: 1px solid #e2e8f0;
}

.area-display {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.popup-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  padding-top: 12px;
  border-top: 1px solid #e2e8f0;
}

.popup-btn {
  flex: 1;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.save-btn {
  background: #3b82f6;
  color: white;
}

.save-btn:hover {
  background: #2563eb;
}

.delete-btn {
  background: #ef4444;
  color: white;
}

.delete-btn:hover {
  background: #dc2626;
}

/* Zones List Popup */
.zones-list-popup {
  width: 280px;
  max-height: 400px;
  padding: 0;
  overflow: hidden;
}

.zones-list-header {
  padding: 12px 16px;
  border-bottom: 1px solid rgba(200, 200, 200, 0.3);
  background: rgba(248, 250, 252, 0.8);
  border-radius: 0.75rem 0.75rem 0 0;
}

.zones-list-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.zones-list-content {
  max-height: 320px;
  overflow: hidden;
}

.empty-zones {
  padding: 24px 16px;
  text-align: center;
  color: #6b7280;
  font-size: 13px;
}

.zones-scroll {
  max-height: 320px;
  overflow-y: auto;
  padding: 8px;
}

.zone-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  margin-bottom: 4px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(200, 200, 200, 0.2);
  transition: all 0.2s ease;
}

.zone-item:hover {
  background: rgba(248, 250, 252, 0.8);
  border-color: rgba(200, 200, 200, 0.4);
}

.zone-info {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  min-width: 0;
}

.zone-color-indicator {
  width: 12px;
  height: 12px;
  border-radius: 3px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.zone-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
  min-width: 0;
  flex: 1;
}

.zone-name {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.zone-area {
  font-size: 11px;
  color: #6b7280;
}

.zone-actions {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.zone-btn {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background: transparent;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.zone-btn:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #374151;
}

.zone-btn.edit-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.zone-btn.zoom-btn:hover {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.zone-btn.delete-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* Scrollbar styling for zones list */
.zones-scroll::-webkit-scrollbar {
  width: 4px;
}

.zones-scroll::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
}

.zones-scroll::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.zones-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Analytics Sidebar */
/* Analytics Sidebar */
.analytics-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: 380px;
  height: 100vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(16px);
  border-left: 1px solid rgba(200, 200, 200, 0.4);
  box-shadow: -8px 0 32px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  display: flex;
  flex-direction: column;
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(200, 200, 200, 0.3);
  background: rgba(248, 250, 252, 0.8);
}

.analytics-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.close-analytics {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  color: #6b7280;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-analytics:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #374151;
}

.analytics-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.analytics-section {
  margin-bottom: 32px;
}

.analytics-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

.analytics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 24px;
}

.analytics-card {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(200, 200, 200, 0.3);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  transition: all 0.2s ease;
}

.analytics-card:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(200, 200, 200, 0.5);
  transform: translateY(-2px);
}

.analytics-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.analytics-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.no-data {
  text-align: center;
  color: #6b7280;
  font-style: italic;
  padding: 32px 16px;
}

.zones-analytics {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.zone-analytics-item {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(200, 200, 200, 0.3);
  border-radius: 10px;
  padding: 16px;
  transition: all 0.2s ease;
}

.zone-analytics-item:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(200, 200, 200, 0.5);
}

.zone-analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.zone-analytics-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.zone-color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.zone-analytics-name {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.zoom-to-zone-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.zoom-to-zone-btn:hover {
  background: rgba(16, 185, 129, 0.2);
  transform: scale(1.05);
}

.zone-analytics-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.zone-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.stat-value {
  font-size: 12px;
  color: #374151;
  font-weight: 600;
}

/* Responsive analytics sidebar */
@media (max-width: 768px) {
  .analytics-sidebar {
    width: 100%;
    left: 0;
  }

  .analytics-grid {
    grid-template-columns: 1fr;
  }
}

/* Loading and Error States */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-left: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-spinner span {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.error-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(239, 68, 68, 0.95);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 9998;
  animation: slideInRight 0.3s ease-out;
}

.error-message button {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.error-message button:hover {
  background: rgba(255, 255, 255, 0.2);
}
