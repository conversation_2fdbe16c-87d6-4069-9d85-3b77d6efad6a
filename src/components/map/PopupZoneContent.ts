import * as turf from "@turf/turf";
import mapboxgl from "mapbox-gl";

type PopupZoneContentProps = {
  map: mapboxgl.Map;
  zone: any;
  toggleZoneDimensions: any;
  zoneDimensionSettings: any;
};

const popupZoneContent = ({
  map,
  zone,
  toggleZoneDimensions,
  zoneDimensionSettings
} : PopupZoneContentProps ) => {
  const zoneGeometry = map.getSource(zone.id)._data.geometry;
  const center = turf.centroid(zoneGeometry).geometry.coordinates;
  const popups = document.getElementsByClassName("mapboxgl-popup");
  for (const popup of popups) {
    popup.remove();
  }

  const popupContent = /* html */ `
    <div class="zone-popup">
      <div class="zone-popup-header">
        <h3>Edit Zone</h3>
      </div>
      <div class="zone-popup-content">
        <div class="popup-field">
          <label>Zone Name:</label>
          <input 
            type="text" 
            id="zoneName-${zone.id}" 
            value="${zone.name}" 
            class="popup-input"
          />
        </div>
        <div class="popup-field">
          <label>Zone Color:</label>
          <div class="color-picker-row">
            <input 
              type="color" 
              id="zoneColor-${zone.id}" 
              value="${window.getZoneColor(zone.id) || zone.color}" 
              class="popup-color-input"
              onchange="window.handleZoneColorChange('${zone.id}', this.value)"
            />
          </div>
        </div>
        <div class="popup-field">
          <label>Area:</label>
          <span class="area-display">${zone.area} m²</span>
        </div>
        <div class="popup-field">
          <label>
            <input 
              type="checkbox" 
              id="showDimensions-${zone.id}" 
              ${!zoneDimensionSettings[zone.id] ? '' : 'checked'}
              onchange="window.toggleZoneDimensionsFromPopup('${zone.id}', this.checked)"
            />
            Show Dimensions
          </label>
        </div>
        <div class="popup-actions">
          <button 
            onclick="window.updateZoneFromPopup('${zone.id}')" 
            class="popup-btn save-btn"
          >
            Save
          </button>
          <button 
            onclick="window.editZoneShape('${zone.id}')" 
            class="popup-btn edit-btn"
          >
            Edit Shape
          </button>
          <button 
            onclick="window.deleteZoneFromPopup('${zone.id}')" 
            class="popup-btn delete-btn"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  `;
  {
    /* Zoom to zone */
  }
  // zoomToZone(zone.id);

  new mapboxgl.Popup({
    closeOnClick: true,
    closeButton: true,
    //   closeOnMove: true,
    maxWidth: "300px",
  })
    .setLngLat(center)
    .setHTML(popupContent)
    .addTo(map);
};

export default popupZoneContent;
