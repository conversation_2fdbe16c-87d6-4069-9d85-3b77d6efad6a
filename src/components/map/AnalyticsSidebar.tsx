import { Target } from "lucide-react";

const AnalyticsSidebar = ({ zones, setShowAnalytics, zoomToZone } :any) => {
  return (
    <div className="analytics-sidebar">
      <div className="analytics-header">
        <h3>Analytics Dashboard</h3>
        <button
          className="close-analytics"
          onClick={() => setShowAnalytics(false)}
        >
          ×
        </button>
      </div>

      <div className="analytics-content">
        {/* Global Analytics */}
        <div className="analytics-section">
          <h4>Global Analytics</h4>
          <div className="analytics-grid">
            <div className="analytics-card">
              <div className="analytics-value">{zones.length}</div>
              <div className="analytics-label">Total Zones</div>
            </div>
            <div className="analytics-card">
              <div className="analytics-value">
                {zones
                  .reduce((total:any, zone:any) => total + parseFloat(zone.area), 0)
                  .toFixed(2)}
              </div>
              <div className="analytics-label">Total Area (m²)</div>
            </div>
            <div className="analytics-card">
              <div className="analytics-value">
                {zones.length > 0
                  ? (
                    zones.reduce(
                      (total:any, zone:any) => total + parseFloat(zone.area),
                      0,
                    ) / zones.length
                  ).toFixed(2)
                  : "0.00"}
              </div>
              <div className="analytics-label">Average Area (m²)</div>
            </div>
            <div className="analytics-card">
              <div className="analytics-value">
                {zones.length > 0
                  ? Math.max(
                    ...zones.map((zone:any) => parseFloat(zone.area)),
                  ).toFixed(2)
                  : "0.00"}
              </div>
              <div className="analytics-label">Largest Zone (m²)</div>
            </div>
          </div>
        </div>

        {/* Per Zone Analytics */}
        <div className="analytics-section">
          <h4>Zone Details</h4>
          {zones.length === 0 ? (
            <div className="no-data">No zones to analyze</div>
          ) : (
            <div className="zones-analytics">
              {zones.map((zone:any) => (
                <div key={zone.id} className="zone-analytics-item">
                  <div className="zone-analytics-header">
                    <div className="zone-analytics-info">
                      <div
                        className="zone-color-dot"
                        style={{ backgroundColor: zone.color }}
                      ></div>
                      <span className="zone-analytics-name">{zone.name}</span>
                    </div>
                    <button
                      className="zoom-to-zone-btn"
                      onClick={() => zoomToZone(zone.id)}
                      title="Zoom to zone"
                    >
                      <Target size={14} />
                    </button>
                  </div>
                  <div className="zone-analytics-stats">
                    <div className="zone-stat">
                      <span className="stat-label">Area:</span>
                      <span className="stat-value">{zone.area} m²</span>
                    </div>
                    <div className="zone-stat">
                      <span className="stat-label">Rank by size:</span>
                      <span className="stat-value">
                        #
                        {zones
                          .sort(
                            (a:any, b:any) => parseFloat(b.area) - parseFloat(a.area),
                          )
                          .findIndex((z:any) => z.id === zone.id) + 1}{" "}
                        of {zones.length}
                      </span>
                    </div>
                    <div className="zone-stat">
                      <span className="stat-label">% of total area:</span>
                      <span className="stat-value">
                        {(
                          (parseFloat(zone.area) /
                            zones.reduce(
                              (total:any, z:any) => total + parseFloat(z.area),
                              0,
                            )) *
                          100
                        ).toFixed(1)}
                        %
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AnalyticsSidebar;
