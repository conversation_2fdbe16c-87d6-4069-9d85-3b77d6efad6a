import { useState, useCallback, useEffect } from "react";
import mapboxgl from "mapbox-gl";
import MapboxDraw from "@mapbox/mapbox-gl-draw";
import * as turf from "@turf/turf";
import AnalyticsSidebar from "./AnalyticsSidebar";
import ZonesListPopup from "./ZonesListPopup";
import popupZoneContent from "./PopupZoneContent";
import { api } from "./api-utils";

import {
  <PERSON>er,
  Pencil,
  Ruler,
  Eye,
  EyeOff,
  List,
  Target,
  BarChart3,
  Tags,
} from "lucide-react";

import "../styles/vericalToolBar.css";

type VericalToolBarPropsT = {
  map: mapboxgl.Map | null;
  draw: MapboxDraw | null;
};

const VerticalToolBar = ({ map, draw }: VericalToolBarPropsT) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTool, setActiveTool] = useState(null);
  const [zones, setZones] = useState([]);
  const [zoneCounter, setZoneCounter] = useState(0);
  const [zonesVisible, setZonesVisible] = useState(true);
  const [selectedColor, setSelectedColor] = useState("#3498db");
  const [selectedName, setSelectedName] = useState("");
  const [showZonesList, setShowZonesList] = useState(false);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(true);
  const [measureMode, setMeasureMode] = useState(false);
  const [showDimensionsGlobal, setShowDimensionsGlobal] = useState(false);
  const [zoneDimensionSettings, setZoneDimensionSettings] = useState({});

  const addDimensionLabels = useCallback(
    (map, zone, feature, flag) => {

      if (!feature.geometry || feature.geometry.type !== "Polygon") return;

      if (flag === false) {
        const labelSourceId = `${zone.id}-dimensions`;
        if (map.getLayer(labelSourceId)) {
          map.removeLayer(labelSourceId);
        }
        if (map.getSource(labelSourceId)) {
          map.removeSource(labelSourceId);
        }
        return;
      }

      const coordinates = feature.geometry.coordinates[0];
      const labelSourceId = `${zone.id}-dimensions`;

      if (map.getLayer(labelSourceId)) {
        map.removeLayer(labelSourceId);
      }
      if (map.getSource(labelSourceId)) {
        map.removeSource(labelSourceId);
      }

      const labelFeatures = [];

      for (let i = 0; i < coordinates.length - 1; i++) {
        const start = coordinates[i];
        const end = coordinates[i + 1];

        const from = turf.point(start);
        const to = turf.point(end);
        const distance = turf.distance(from, to, { units: "meters" });

        const midpoint = [(start[0] + end[0]) / 2, (start[1] + end[1]) / 2];

        let bearing = turf.bearing(from, to);
        if (bearing > 90) {
          bearing = bearing - 180;
        } else if (bearing < -90) {
          bearing = bearing + 180;
        }

        labelFeatures.push({
          type: "Feature",
          geometry: {
            type: "Point",
            coordinates: midpoint,
          },
          properties: {
            distance: `${distance.toFixed(1)}m`,
            edgeIndex: i,
            bearing: bearing,
          },
        });
      }

      map.addSource(labelSourceId, {
        type: "geojson",
        data: {
          type: "FeatureCollection",
          features: labelFeatures,
        },
      });


      map.addLayer({
        id: labelSourceId,
        type: "symbol",
        source: labelSourceId,
        layout: {
          "text-field": ["get", "distance"],
          "text-font": ["Open Sans Regular", "Arial Unicode MS Regular"],
          "text-size": 11,
          "text-anchor": "center",
          "text-rotation-alignment": "map",
          'text-rotate': ['get', 'bearing'],
          // 'text-rotate': ['interpolate', ['linear'], ['zoom'], 10, , 15, 90],
          "text-allow-overlap": true,
          "text-ignore-placement": true,
        },
        paint: {
          // 'text-color': zone.color || '#3498db',
          "text-color": "#ff0000",
          "text-halo-color": "#ffffff",
          "text-halo-width": 1.5,
        },
      });
    },
    []
  );

  const toggleGlobalDimensions = useCallback(() => {
    const flag = !showDimensionsGlobal;
    setShowDimensionsGlobal(flag);
    zones.forEach((zone) => {
      const feature = map?.getSource(zone.id)?._data;
      if (feature) {
        addDimensionLabels(map, zone, feature, flag);
      }
    });

  }, [showDimensionsGlobal, zones, map, addDimensionLabels]);

  const toggleZoneDimensions = useCallback(

    (zoneId) => {

      const flag = !zoneDimensionSettings[zoneId];
      setZoneDimensionSettings((prev) => ({
        ...prev,
        [zoneId]: flag,
      }));

      const zone = zones.find((z) => z.id === zoneId);
      const feature = map?.getSource(zoneId)?._data;

      if (zone && feature) {

        addDimensionLabels(map, zone, feature, flag);
      }
    },
    [zones, map, addDimensionLabels]
  );

  const addZoneToMap = useCallback(
    (zone) => {
      if (!map) return;

      if (!zone || !zone.id) {
        console.error("Invalid zone data:", zone);
        setError("Invalid zone data: " + JSON.stringify(zone));
        return;
      }

      const feature = {
        type: "Feature",
        geometry: {
          type: "Polygon",
          coordinates: [zone.coordinates], // array of arrays for Polygon
        },
        properties: {
          id: zone.id,
          name: zone.name,
          color: zone.color,
        },
      };

      console.log("Generated feature:", feature);

      try {
        const drawFeatureId = `${zone.id}`;
        const drawFeature = {
          ...feature,
          id: drawFeatureId,
        };

        const existingFeatures = draw.getAll();
        const existingFeature = existingFeatures.features.find(
          (f) => f.id === drawFeatureId
        );
        if (existingFeature) {
          // draw.delete(drawFeatureId);
        }

        // // Add to Draw instance
        draw.add(drawFeature);

        // // Update zone with the correct drawId
        setZones((prevZones) =>
          prevZones.map((z) =>
            z.id === zone.id ? { ...z, drawId: drawFeatureId } : z
          )
        );

        // // Check if layer already exists and remove it
        // if (map.getLayer(String(zone.id))) {
        //   map.removeLayer(String(zone.id));
        // }
        // if (map.getLayer(String(zone.id) + "-border")) {
        //   map.removeLayer(String(zone.id) + "-border");
        // }
        // if (map.getSource(String(zone.id))) {
        //   map.removeSource(String(zone.id));
        // }

        // Add source first
        map.addSource(String(zone.id), {
          type: "geojson",
          data: feature,
        });

        // Add colored layer for the zone
        map.addLayer({
          id: String(zone.id),
          type: "fill",
          source: String(zone.id),
          paint: {
            "fill-color": zone.color || "#3498db",
            "fill-opacity": 0.4,
          },
        });

        // Add border
        map.addLayer({
          id: String(zone.id) + "-border",
          type: "line",
          source: String(zone.id),
          paint: {
            "line-color": zone.color || "#3498db",
            "line-width": 3,
          },
        });

        map.on("click", String(zone.id), () => {
          popupZoneContent({
            map,
            zone,
            toggleZoneDimensions,
            zoneDimensionSettings,
          });
        });

        console.log(`Successfully added zone ${zone.id} to map`);
      } catch (error) {
        console.error("Error adding zone to map:", error);
      }
    },
    [map, draw, toggleZoneDimensions, zoneDimensionSettings]
  );

  const normalizedZoneData = (zone) => {
    let coordinates = [];
    if (zone.polygon) {
      if (Array.isArray(zone.polygon) && zone.polygon.length > 0) {
        coordinates = zone.polygon[0];
      } else if (zone.polygon.coordinates) {
        coordinates = zone.polygon.coordinates[0] || zone.polygon.coordinates;
      }
    }

    return {
      id: String(zone.id),
      name: zone.name || `Zone ${zone.id}`,
      color: zone.color || "#3498db",
      area: zone.area || "0",
      coordinates: coordinates,
      drawId: zone.drawId || zone.id,
    };
  };

  useEffect(() => {
    const loadZones = async () => {
      if (!map) return;

      setLoading(true);
      setError(null);

      try {
        const zonesData = await api.fetchZones();
        const normalizedZones = Array.isArray(zonesData)
          ? zonesData.map((zone) => {
              return normalizedZoneData(zone);
            })
          : [];
        setZones(normalizedZones);
        setZoneCounter(normalizedZones.length);
        setTimeout(() => {
          normalizedZones.forEach((zone) => {
            if (zone.coordinates && zone.coordinates.length > 0) {
              addZoneToMap(zone);
            } else {
              console.warn(`Zone ${zone.id} has no valid coordinates:`, zone);
            }
          });
        }, 100);
      } catch (err) {
        console.error("Error loading zones:", err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    loadZones();
  }, [map]);

  const updateZone = useCallback(
    async (zoneId, newName, newColor) => {
      if (!map) return;

      try {
        setLoading(true);

        const updateData = { name: newName, color: newColor };
        await api.updateZone(zoneId, updateData);

        setZones((prevZones) =>
          prevZones.map((zone) =>
            zone.id === zoneId
              ? { ...zone, name: newName, color: newColor }
              : zone
          )
        );

        if (map.getLayer(zoneId)) {
          map.setPaintProperty(zoneId, "fill-color", newColor);
        }
        if (map.getLayer(zoneId + "-border")) {
          map.setPaintProperty(zoneId + "-border", "line-color", newColor);
        }
        if (map.getLayer(zoneId + "-dimensions")) {
          map.setPaintProperty(zoneId + "-dimensions", "text-color", newColor);
        }
      } catch (err) {
        console.error("Error updating zone:", err);
        setError(err.message);
        alert("Failed to update zone. Please try again.");
      } finally {
        setLoading(false);
      }
    },
    [map, api]
  );

  const createZone = useCallback(
    async (feature, color, name) => {
      if (!map) return;

      const area = turf.area(feature);
      const areaInSquareMeters = area.toFixed(2);

      const zoneData = {
        drawId: feature.id,
        name: name,
        color: color,
        area: areaInSquareMeters,
        coordinates: feature.geometry.coordinates[0],
      };

      try {
        setLoading(true);
        const savedZone = await api.createZone(zoneData);
        addZoneToMap(normalizedZoneData(savedZone));
      } catch (err) {
        console.error("Error creating zone:", err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    },
    [map, addZoneToMap]
  );

  const deleteZone = useCallback(
    async (zoneId) => {
      if (!map) return;

      const zoneToDelete = zones.find((z) => z.id === zoneId);
      if (!zoneToDelete) return;

      try {
        setLoading(true);

        await api.deleteZone(zoneId);

        if (map.getLayer(zoneId)) {
          map.removeLayer(zoneId);
        }
        if (map.getLayer(zoneId + "-border")) {
          map.removeLayer(zoneId + "-border");
        }
        if (map.getLayer(zoneId + "-dimensions")) {
          map.removeLayer(zoneId + "-dimensions");
        }
        if (map.getSource(zoneId + "-dimensions")) {
          map.removeSource(zoneId + "-dimensions");
        }
        if (map.getSource(zoneId)) {
          map.removeSource(zoneId);
        }

        map.off("click", zoneId);

        if (draw && zoneToDelete.drawId) {
          draw.delete(zoneToDelete.drawId);
        }

        setZones((prevZones) => prevZones.filter((zone) => zone.id !== zoneId));
      } catch (err) {
        console.error("Error deleting zone:", err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    },
    [map, draw, zones]
  );

  useEffect(() => {
    window.toggleZoneDimensionsFromPopup = (zoneId) => {
      toggleZoneDimensions(zoneId);
    };

    window.updateZoneFromPopup = (zoneId) => {
      const nameInput = document.getElementById(`zoneName-${zoneId}`);
      const colorInput = document.getElementById(`zoneColor-${zoneId}`);

      if (nameInput && colorInput) {
        updateZone(zoneId, nameInput.value, colorInput.value);

        const popups = document.getElementsByClassName("mapboxgl-popup");
        for (const popup of popups) {
          popup.remove();
        }
      }
    };

    window.editZoneShape = (zoneId) => {
      console.log("Edit zone shape called for:", zoneId);
      if (!draw) {
        console.log("Draw instance not available");
        return;
      }

      const zone = zones.find((z) => z.id === zoneId);
      console.log("Found zone:", zone);
      if (!zone || !zone.drawId) {
        console.log("Zone not found or missing drawId:", zone);
        return;
      }

      const allFeatures = draw.getAll();
      const drawFeature = allFeatures.features.find(
        (f) => f.id === zone.drawId
      );

      if (!drawFeature) {
        const feature = {
          type: "Feature",
          id: zone.drawId,
          geometry: {
            type: "Polygon",
            coordinates: [zone.coordinates],
          },
          properties: {
            id: zone.id,
            name: zone.name,
            color: zone.color,
          },
        };
        draw.add(feature);
      }

      const popups = document.getElementsByClassName("mapboxgl-popup");
      for (const popup of popups) {
        popup.remove();
      }

      try {
        draw.changeMode("direct_select", { featureId: zone.drawId });
        setActiveTool("edit");
      } catch (error) {
        console.error("Error entering direct_select mode:", error);
      }
    };

    window.deleteZoneFromPopup = (zoneId) => {
      if (confirm("Are you sure you want to delete this zone?")) {
        deleteZone(zoneId);

        const popups = document.getElementsByClassName("mapboxgl-popup");
        for (const popup of popups) {
          popup.remove();
        }
      }
    };

    window.getZoneColor = (zoneId) => {
      const zone = zones.find((z) => z.id === zoneId);
      console.log("Getting zone color for:", zoneId, zone);
      return zone ? zone.color : null;
    };

    window.handleZoneColorChange = (zoneId: number, newColor: string) => {
      if (map && map.getLayer(zoneId)) {
        map.setPaintProperty(zoneId, "fill-color", newColor);
      }
      if (map && map.getLayer(zoneId + "-border")) {
        map.setPaintProperty(zoneId + "-border", "line-color", newColor);
      }

      setZones((currentZones) => {
        return currentZones.map((zone) => {
          if (zone.id === zoneId) {
            console.log(
              "Updating zone color in state:",
              zone.id,
              zoneId,
              newColor,
              zone
            );

            return {
              ...zone,
              color: newColor,
            };
          }

          return zone;
        });
      });
    };

    return () => {
      delete window.toggleZoneDimensionsFromPopup;
      delete window.updateZoneFromPopup;
      delete window.editZoneShape;
      delete window.deleteZoneFromPopup;
    };
  }, [updateZone, deleteZone, draw, map, zones, toggleZoneDimensions]);

  useEffect(() => {
    if (!map) return;

    const handleDrawCreate = (e) => {
      const feature = e.features[0];

      if (measureMode && feature.geometry.type === "LineString") {
        const length = turf.length(feature, { units: "meters" });
        const coords = feature.geometry.coordinates;
        const lastCoord = coords[coords.length - 1];

        new mapboxgl.Popup({
          closeOnClick: false,
          closeButton: true,
        })
          .setLngLat(lastCoord)
          .setHTML(
            `<div class="measure-popup">Distance: ${length.toFixed(
              2
            )} meters</div>`
          )
          .addTo(map);

        setTimeout(() => {
          draw?.delete(feature.id);
        }, 100);

        return;
      }

      const color = selectedColor;
      const name = selectedName || `Zone ${zoneCounter + 1}`;

      createZone(feature, color, name);
      setSelectedName("");
      setZoneCounter((prev) => prev + 1);
      setActiveTool(null); // Reset tool after drawing
    };

    map.on("draw.create", handleDrawCreate);

    const handleDrawUpdate = async (e) => {
      console.log("Draw update event triggered", e);
      const updatedFeature = e.features[0];
      console.log("Updated feature:", updatedFeature);

      const zone = zones.find((z) => z.drawId === updatedFeature.id);
      console.log("Found zone for update:", zone);

      if (zone) {
        try {
          setLoading(true);
          const area = turf.area(updatedFeature);
          const areaInSquareMeters = area.toFixed(2);
          const updateData = {
            polygon: [updatedFeature.geometry.coordinates[0]], // Backend expects polygon format
            area: areaInSquareMeters,
          };

          await api.updateZone(zone.id, updateData);

          if (map.getSource(zone.id)) {
            map.getSource(zone.id).setData(updatedFeature);
          }

          setZones((prevZones) => {
            const updatedZones = prevZones.map((z) =>
              z.id === zone.id
                ? {
                    ...z,
                    area: areaInSquareMeters,
                    coordinates: updatedFeature.geometry.coordinates[0],
                  }
                : z
            );
            return updatedZones;
          });

        } catch (err) {
          console.error("Error updating zone shape:", err);
          setError("Failed to save zone changes. Please try again.");
        } finally {
          setLoading(false);
        }
      }

      setActiveTool(null);
    };

    map.on("draw.update", handleDrawUpdate);

    return () => {
      map.off("draw.create", handleDrawCreate);
      map.off("draw.update", handleDrawUpdate);
    };
  }, [
    map,
    selectedColor,
    selectedName,
    createZone,
    zoneCounter,
    measureMode,
    draw,
    zones,
  ]);

  const toggleZonesVisibility = () => {
    const newVisibility = !zonesVisible;
    setZonesVisible(newVisibility);

    zones.forEach((zone) => {
      try {
        map.setLayoutProperty(
          zone.id,
          "visibility",
          newVisibility ? "visible" : "none"
        );
        map.setLayoutProperty(
          zone.id + "-border",
          "visibility",
          newVisibility ? "visible" : "none"
        );
      } catch (e) {
        console.log("Layer might not exist:", e);
      }
    });
  };

  const zoomToZone = useCallback(
    (zoneId) => {
      if (!map) return;

      const zone = zones.find((z) => z.id === zoneId);
      if (!zone) return;

      try {
        const source = map.getSource(zoneId);
        if (source && source._data) {
          const bounds = turf.bbox(source._data);
          map.fitBounds(bounds, {
            padding: { top: 50, bottom: 50, left: 50, right: 50 },
            duration: 1000,
          });
        }
      } catch (e) {
        console.log("Error zooming to zone:", e);
      }
    },
    [map, zones]
  );

  const zoomToAllZones = useCallback(() => {
    if (!map || zones.length === 0) return;

    try {
      const features = [];
      zones.forEach((zone) => {
        const source = map.getSource(zone.id);
        if (source && source._data) {
          features.push(source._data);
        }
      });

      if (features.length > 0) {
        const collection = turf.featureCollection(features);
        const bounds = turf.bbox(collection);
        map.fitBounds(bounds, {
          padding: { top: 80, bottom: 80, left: 80, right: 80 },
          duration: 1000,
        });
      }
    } catch (e) {
      console.log("Error zooming to all zones:", e);
    }
  }, [map, zones]);

  const tools = [
    {
      id: "select",
      label: "Select",
      icon: Pointer,
      action: () => draw?.changeMode("simple_select"),
    },
    {
      id: "draw",
      label: "Draw Zone",
      icon: Pencil,
      action: () => draw?.changeMode("draw_polygon"),
    },
    {
      id: "measure",
      label: "Measure",
      icon: Ruler,
      action: () => {
        setMeasureMode(!measureMode);
        if (!measureMode) {
          draw?.changeMode("draw_line_string");
        } else {
          draw?.changeMode("simple_select");
        }
      },
    },
    {
      id: "zones-list",
      label: "Zones List",
      icon: List,
      action: () => setShowZonesList(!showZonesList),
    },
    {
      id: "analytics",
      label: "Analytics",
      icon: BarChart3,
      action: () => setShowAnalytics(!showAnalytics),
    },
    {
      id: "zoom-all",
      label: "Zoom to All Zones",
      icon: Target,
      action: zoomToAllZones,
      disabled: zones.length === 0,
    },
    {
      id: "visibility",
      label: zonesVisible ? "Hide Zones" : "Show Zones",
      icon: zonesVisible ? Eye : EyeOff,
      action: toggleZonesVisibility,
    },
    {
      id: "dimensions",
      label: showDimensionsGlobal ? "Hide Dimensions" : "Show Dimensions",
      icon: Tags,
      action: toggleGlobalDimensions,
    },
  ];

  const handleToolClick = (tool) => {
    setActiveTool(activeTool === tool.id ? null : tool.id);
    if (tool.action) {
      tool.action();
    }
  };

  return (
    <>
      {/* loading indicator */}
      {loading && (
        <div className="loading-overlay">
          <div className="loading-spinner">
            <div className="spinner"></div>
            <span>Syncing zones...</span>
          </div>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="error-message">
          <span>{error}</span>
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      <div className={`vertical-toolbar ${isCollapsed ? "collapsed" : ""}`}>
        {/* Toggle button */}
        <button
          className={`menu-toggle ${isCollapsed ? "" : "collapsed"}`}
          onClick={() => setIsCollapsed(!isCollapsed)}
          title={isCollapsed ? "Show tools" : "Hide tools"}
        >
          <div className="hamburger">
            <span className="hamburger-line"></span>
            <span className="hamburger-line"></span>
            <span className="hamburger-line"></span>
          </div>
          <div className="ripple"></div>
        </button>

        {/* Tools - only show when not collapsed */}
        {!isCollapsed && (
          <>
            {tools.map((tool) => (
              <div key={tool.id} className="tool-container">
                <button
                  onClick={() => handleToolClick(tool)}
                  className={`tool-button ${
                    activeTool === tool.id ? "active" : ""
                  } ${tool.danger ? "danger" : ""} ${
                    tool.disabled ? "disabled" : ""
                  }`}
                  title={tool.label}
                  disabled={tool.disabled}
                >
                  <tool.icon className="tool-icon" />
                </button>

                {/* Zones list popup */}
                {tool.id === "zones-list" && showZonesList && (
                  <ZonesListPopup
                    map={map}
                    zones={zones}
                    zoomToZone={zoomToZone}
                    zoneDimensionSettings={zoneDimensionSettings}
                    deleteZone={deleteZone}
                  />
                )}
              </div>
            ))}
          </>
        )}
      </div>
      {/* Analytics Sidebar */}
      {showAnalytics && (
        <AnalyticsSidebar
          zones={zones}
          zoomToZone={zoomToZone}
          setShowAnalytics={setShowAnalytics}
        />
      )}
    </>
  );
};

export default VerticalToolBar;