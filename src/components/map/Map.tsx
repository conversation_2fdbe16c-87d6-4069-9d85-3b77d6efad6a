"use client";

import { useRef, useEffect, useState } from "react";
/* Mapbox */
import mapboxgl, { type LngLatLike } from "mapbox-gl";
import MapboxDraw from "@mapbox/mapbox-gl-draw";

/** styles **/
import "mapbox-gl/dist/mapbox-gl.css";
import "@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css";

/* debugging */
import { Toaster, toast } from "react-hot-toast";

import VerticalToolBar from "./VerticalToolbar";

/* Constants */ /* TODO: To remove */
const INITIAL_CENTER: LngLatLike = [-8.95442, 30.75374]; // Morocco
const INITIAL_ZOOM = 5;

type MapProps = {
  showVerticalToolBar : boolean ;
};

const Map = ({showVerticalToolBar = true} : MapProps) => {
  const mapRef = useRef<mapboxgl.Map | null>(null);
  const mapContainerRef = useRef<HTMLDivElement>(undefined);
  const [draw, setDraw] = useState<MapboxDraw | null>(null);
  const [mapInstance, setMapInstance] = useState<mapboxgl.Map | null>(null);

  useEffect(() => {
    try {
      mapboxgl.accessToken =
        "pk.eyJ1IjoibW9iZXJyciIsImEiOiJjbWY1cHFqNHAwN3JhMm1zOWRsbXJzZTY0In0.fy_RfMNb4i0_TXbvkLJs_Q";

      if (mapContainerRef.current) {
        mapRef.current = new mapboxgl.Map({
          container: mapContainerRef.current,
          style: "mapbox://styles/mapbox/satellite-v9",
          center: INITIAL_CENTER,
          zoom: INITIAL_ZOOM,
        });
      } else throw Error("Map Container Not Found!");

      /* Add navigation control (the +/- zoom buttons) */
      mapRef.current.addControl(new mapboxgl.NavigationControl(), "top-right");

      /* Add geolocate control (the location button) */
      mapRef.current.addControl(new mapboxgl.GeolocateControl(), "top-right");

      /* Add fullscreen control (the full screen button) */
      mapRef.current.addControl(new mapboxgl.FullscreenControl(), "top-right");

      /* Add scale control (the scale bar) */
      mapRef.current.addControl(new mapboxgl.ScaleControl(), "bottom-right");

      /* Add a marker to the map */
      // TODO: enable this later if needed
      // const marker = new mapboxgl.Marker({ color: "red", draggable: false })
      //   .setLngLat(INITIAL_CENTER)
      //   .addTo(mapRef.current);

      // /* Add popup to the marker */
      // const popup = new mapboxgl.Popup({ offset: 25 }).setText("Marker Popup");
      // marker.setPopup(popup);

      /* Initialize drawing tools */
      const Draw = new MapboxDraw({
        displayControlsDefault: false,
        controls: {
          polygon: true,
          trash: true,
        },
        defaultMode: "simple_select",
      });
      // mapRef.current!.addControl(draw as unknown as mapboxgl.IControl, 'bottom-left');
      //commented by salah
      mapRef.current.addControl(Draw, "bottom-left");
      setDraw(Draw);
      setMapInstance(mapRef.current);

      mapRef.current.on("load", function() {
        // TODO: load styles
      });
    } catch (error) {
      toast.error("Error initializing map:" + error);
    }

    if (mapRef.current) {
      mapRef.current.on("move", () => {
        // TODO: handle center changes
        // setCenter([
        //   mapRef.current.getCenter().lng,
        //   mapRef.current.getCenter().lat,
        // ]);
        // setZoom(mapRef.current.getZoom());
      });
    }

    return () => {
      mapRef?.current?.remove();
    };
  }, []);

  return (
    <div className="container">
      <Toaster position="top-right" />
      {/* <ZoneMapperPanel map={mapInstance} draw={draw} /> */}
      <div className="map-section">
        <div className="map-overlay">
          {/* <div className="coordinates-display">
            <p>Longitude: {center[0].toFixed(4)}</p>
            <p> Latitude: {center[1].toFixed(4)}</p>
            <p> Zoom: {zoom.toFixed(2)}</p>
            <button className="cursor-pointer reset-button" onClick={handleResetbtn}>
              Reset
            </button>
          </div> */}
        </div>
        <div
          ref={mapContainerRef as React.Ref<HTMLDivElement>}
          id="map-container"
        >
        {showVerticalToolBar && <VerticalToolBar map={mapInstance} draw={draw} />}
        </div>
      </div>
    </div>
  );
};

export default Map;
