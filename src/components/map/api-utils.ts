const API_BASE_URL = "http://localhost:8080/v1";

const getAccessToken = (): string | null => {
  if (typeof window === 'undefined') return null; // SSR/Node guard
  try {
    return window.localStorage.getItem('access');
  } catch {
    return null;
  }
};

const getCookieToken = (name: string): string | null => {
  if (typeof document === 'undefined') return null;
  const m = document.cookie.match(new RegExp(`(?:^|; )${name}=([^;]*)`));
  return m ? decodeURIComponent(m[1]) : null;
};

const authHeaders = (): HeadersInit => {
  const token = getAccessToken() ?? getCookieToken('auth_token');
  return token ? { Authorization: `Bearer ${token}` } : {};
};

const fetchZones = async () => {
  const response = await fetch(`${API_BASE_URL}/zones/`, {
    headers: {
      ...authHeaders(),
    },
    // credentials: 'include', // uncomment if using cookies/auth on same domain
  });
  if (!response.ok) throw new Error("Failed to fetch zones");
  return response.json();
};

const createZone = async (zoneData: {
  color: string;
  name: string;
  coordinates: Array<Array<number>>;
}) => {
  const data = {
    color: zoneData.color,
    name: zoneData.name,
    polygon: [zoneData.coordinates],
  };
  const response = await fetch(`${API_BASE_URL}/zones/`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      ...authHeaders(),
    },
    body: JSON.stringify(data),
  });
  if (!response.ok) throw new Error("Failed to create zone");
  return response.json();
};

const updateZone = async (zoneId: string, zoneData: any) => {
  const response = await fetch(`${API_BASE_URL}/zones/${zoneId}/`, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
      ...authHeaders(),
    },
    body: JSON.stringify(zoneData),
  });
  if (!response.ok) throw new Error("Failed to update zone");
  return response.json();
};

const deleteZone = async (zoneId: string) => {
  const response = await fetch(`${API_BASE_URL}/zones/${zoneId}/`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      ...authHeaders(),
    },
  });
  if (!response.ok) throw new Error("Failed to delete zone");
  return response.status === 204 ? { success: true } : response.json();
};

export const api = { fetchZones, createZone, updateZone, deleteZone };
