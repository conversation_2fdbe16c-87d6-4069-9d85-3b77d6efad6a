import { Pencil, Trash2, Target } from "lucide-react";
import popupZoneContent from "./PopupZoneContent";

const ZonesListPopup = ({ map, zones, zoomToZone, zoneDimensionSettings, deleteZone }) => {
  return (
    <div className="tool-popup zones-list-popup">
      <div className="zones-list-header">
        <h4>Zones ({zones.length})</h4>
      </div>
      <div className="zones-list-content">
        {zones.length === 0 ? (
          <div className="empty-zones">No zones created yet</div>
        ) : (
          <div className="zones-scroll">
            {zones.map((zone) => (
              <div key={zone.id} className="zone-item">
                <div className="zone-info">
                  <div
                    className="zone-color-indicator"
                    style={{ backgroundColor: zone.color }}
                  ></div>
                  <div className="zone-details">
                    <span className="zone-name">{zone.name}</span>
                    <span className="zone-area">{zone.area} m²</span>
                  </div>
                </div>
                <div className="zone-actions">
                  <button
                    className="zone-btn zoom-btn"
                    onClick={() => zoomToZone(zone.id)}
                    title="Zoom to zone"
                  >
                    <Target size={12} />
                  </button>
                  <button
                    className="zone-btn edit-btn"
                    onClick={() => { popupZoneContent({ map, zone, zoneDimensionSettings }); }}
                    title="Edit zone"
                  >
                    <Pencil size={12} />
                  </button>
                  <button
                    className="zone-btn delete-btn"
                    onClick={() => {
                      if (confirm(`Delete zone "${zone.name}"?`)) {
                        deleteZone(zone.id);
                      }
                    }}
                    title="Delete zone"
                  >
                    <Trash2 size={12} />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
export default ZonesListPopup;
