import React from 'react';
import { TrendingUp } from 'lucide-react';

interface StatCardData {
  title: string;
  value: string;
  change: string;
  trend: 'up' | 'down';
  icon: React.ReactNode;
  color: string;
  percentage: number;
}

interface StatCardProps {
  data: StatCardData;
}

const StatCard: React.FC<StatCardProps> = ({ data }) => {
  return (
    <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg hover:border-gray-200 transition-all duration-300 group">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <p className="text-gray-600 text-sm font-medium">{data.title}</p>
          </div>
          <p className="text-3xl font-bold text-gray-900 mb-2">{data.value}</p>
          <div className="flex items-center space-x-2">
            <div className={`flex items-center space-x-1 ${
              data.trend === 'up' ? 'text-green-600' : 'text-red-500'
            }`}>
              <TrendingUp className={`w-4 h-4 ${
                data.trend === 'down' ? 'rotate-180' : ''
              }`} />
              <span className="text-sm font-semibold">{data.change}</span>
            </div>
          </div>
          
         
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ${data.color}`}
                style={{ width: `${data.percentage}%` }}
              />
            </div>
          </div>
        </div>
        
        <div className={`p-3 rounded-2xl ${data.color} group-hover:scale-110 transition-transform duration-300`}>
          {data.icon}
        </div>
      </div>
    </div>
  );
};

export default StatCard;