'use client';

import React from 'react';
import { useAppSelector } from '@/lib/hooks';
import { useGetAuthDataQuery } from '@/lib/store/authApi';

export default function AuthWrapper({ children }: { children: React.ReactNode }) {
  const token = useAppSelector((s:any) => s.auth.token);
useGetAuthDataQuery({ token: token ?? '' }, { skip: !token });
  return <>{children}</>;
}







// 'use client';

// import { useEffect } from 'react';
// import { useRouter } from 'next/navigation';
// import { useAppSelector } from '@/lib/hooks';
// import { useGetAuthDataQuery } from '@/lib/store/authApi';
// import { getValidAuthTokens } from '@/lib/utils/cookies';

// interface AuthWrapperProps {
//   children: React.ReactNode;
// }

// export default function AuthWrapper({ children }: AuthWrapperProps) {
//   const router = useRouter();
//   const { user, isAuthenticated } = useAppSelector((state) => state.auth);
//   const { token } = getValidAuthTokens();

//   const { error, isLoading } = useGetAuthDataQuery(
//     { token: token || '' },
//     {
//       skip: !token || !!user,
//     }
//   );

//   useEffect(() => {
//     if (!token && !isAuthenticated) {
//       router.push('/auth/login');
//     }
//   }, [token, isAuthenticated, router]);

//   if (isLoading) {
//     return (
//       <div className="flex items-center justify-center min-h-screen">
//         <div className="text-center">
//           <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
//           <p className="mt-2">Loading...</p>
//         </div>
//       </div>
//     );
//   }

//   if (error && !isAuthenticated) {
//     router.push('/auth/login');
//     return null;
//   }

//   return <>{children}</>;
// }
