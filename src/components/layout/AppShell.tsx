"use client";

import { useMemo, useState } from "react";
import { usePathname, useRouter } from "next/navigation";

// Use YOUR existing components:
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";

const routeMap: Record<string, string> = {
  dashboard: "/dashboard",
  map: "/dashboard/map",
  "water-plan": "/dashboard/water-plan",
  "water-inputs": "/dashboard/water-inputs",
  configuration: "/dashboard/configuration",
  reports: "/dashboard/reports",
  settings: "/dashboard/settings",
};

export default function AppShell({ children }: { children: React.ReactNode }) {
  const [open, setOpen] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  // derive active id from URL first segment
  const activeId = useMemo(() => {
    const seg = (pathname?.split("/")[1] || "dashboard").toLowerCase();
    return Object.keys(routeMap).includes(seg) ? seg : "dashboard";
  }, [pathname]);

  const handleSectionChange = (id: string) => {
    const href = routeMap[id] || "/dashboard/ComingSoonPage";
    router.push(href);
    setOpen(false);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Sidebar
        isOpen={open}
        onClose={() => setOpen(false)}
        activeSection={activeId}
        onSectionChange={handleSectionChange}
      />
      <div className="lg:pl-72"> {/* keep in sync with Sidebar width */}
        <Header onMenuClick={() => setOpen(true)} />
        <main className="px-4 sm:px-6 lg:px-8 py-6">{children}</main>
      </div>
    </div>
  );
}
