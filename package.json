{"name": "app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack --no-lint", "start": "next start", "lint": "eslint"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@reduxjs/toolkit": "^2.9.0", "class-variance-authority": "^0.7.1", "cookies-next": "^6.1.0", "lucide-react": "^0.544.0", "next": "15.5.3", "react": "19.1.0", "react-dom": "19.1.0", "react-redux": "^9.2.0", "recharts": "^3.2.0", "tailwind-merge": "^3.3.1", "@mapbox/mapbox-gl-draw": "^1.5.0", "@turf/turf": "^7.2.0", "mapbox-gl": "^3.15.0", "react-hot-toast": "^2.6.0", "turf": "^3.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.3", "tailwindcss": "^4.1.13", "typescript": "^5"}}