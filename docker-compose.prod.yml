# Docker Compose for Farmti Frontend - Production Environment
# Optimized for performance and security

version: "3.9"

services:
  farmti-prod:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: farmti-prod
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=production
    restart: always
    # Health check to ensure the app is running
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
