# Farmti Frontend

Smart Farm Management Platform - A Next.js application for intelligent agricultural management.

## 🚀 Quick Start

### Development Environment

Run the development server with hot-reload:

```bash
docker compose -f docker-compose.dev.yml up --build
```

Visit 👉 **http://localhost:3001** in your browser

### Production Environment

Run the optimized production build:

```bash
docker compose -f docker-compose.prod.yml up --build -d
```

Visit 👉 **http://localhost:3001** in your browser

## 📚 Documentation

For detailed Docker setup information, troubleshooting, and best practices, see [DOCKER_SETUP.md](./DOCKER_SETUP.md)

## 🛠️ Tech Stack

- **Framework:** Next.js 15 with React 19
- **Styling:** Tailwind CSS
- **State Management:** Redux Toolkit
- **Maps:** Mapbox GL
- **Charts:** Recharts
- **Deployment:** Docker
